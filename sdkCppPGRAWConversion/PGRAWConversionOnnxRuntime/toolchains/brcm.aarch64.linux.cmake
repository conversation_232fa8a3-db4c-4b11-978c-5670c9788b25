# For Docker you need:
# apt-get install -y libc6-i386 lib32stdc++6 lib32z1 lib32ncurses5 lib32z1 libc6-dev-i386
# export BRCM_SDK_HOME=/opt/brcm
# export LD_LIBRARY_PATH=$BRCM_SDK_HOME/usr/lib
# export PATH=$BRCM_SDK_HOME/usr/bin:$PATH

set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR aarch64)

set(BRCM_SDK_ROOT $ENV{BRCM_SDK_HOME})

set(BRCM_C_COMPILER "${BRCM_SDK_ROOT}/bin/aarch64-linux-gcc")
set(BRCM_CXX_COMPILER "${BRCM_SDK_ROOT}/bin/aarch64-linux-g++")
set(BRCM_SDK_SYSROOT "${BRCM_SDK_ROOT}/aarch64-buildroot-linux-gnu/sysroot")

set(CMAKE_C_COMPILER ${BRCM_C_COMPILER})
set(CMAKE_CXX_COMPILER ${BRCM_CXX_COMPILER})
set(CMAKE_FIND_ROOT_PATH ${BRCM_SDK_SYSROOT})

set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_PACKAGE ONLY)

# set(THREADS_PTHREAD_ARG "0" CACHE STRING "Result from TRY_RUN" FORCE)
# set(CMAKE_SYSROOT ${BRCM_SDK_SYSROOT})
# set(CMAKE_EXE_LINKER_FLAGS "-L${BRCM_SDK_SYSROOT}/lib")
# set(CMAKE_SHARED_LINKER_FLAGS "-L${BRCM_SDK_SYSROOT}/lib")
# set(CMAKE_MODULE_LINKER_FLAGS "-L${BRCM_SDK_SYSROOT}/lib")
# set(CMAKE_CXX_FLAGS "-std=c++11 -march=armv8-a ${CMAKE_CXX_FLAGS}")
