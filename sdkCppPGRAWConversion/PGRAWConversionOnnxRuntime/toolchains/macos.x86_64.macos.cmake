set(CMAKE_THREAD_LIBS_INIT "-lpthread")
set(CMAKE_HAVE_THREADS_LIBRARY 1)
set(CMAKE_USE_WIN32_THREADS_INIT 0)
set(CMAKE_USE_PTHREADS_INIT 1)
set(USED_CMAKE_GENERATOR "${CMAKE_GENERATOR}" CACHE STRING "Expose CMAKE_GENERATOR" FORCE)

execute_process(COMMAND xcodebuild -version OUTPUT_VARIABLE XCODE_VERSION ERROR_QUIET OUTPUT_STRIP_TRAILING_WHITESPACE)
string(R<PERSON><PERSON> MATCH "Xcode [0-9\\.]+" XCODE_VERSION "${XCODE_VERSION}")
string(REGEX REPLACE "Xcode ([0-9\\.]+)" "\\1" XCODE_VERSION "${XCODE_VERSION}")
# message(STATUS "Building with Xcode version: ${XCODE_VERSION}")

if(NOT XCODE_OS_ARCH)
    set(XCODE_OS_ARCH x86_64)
endif()
set(XCODE_OS_PLATFORM macosx)

execute_process(COMMAND xcodebuild -version -sdk ${XCODE_OS_PLATFORM} Path OUTPUT_VARIABLE XCODE_SDK_ROOT ERROR_QUIET OUTPUT_STRIP_TRAILING_WHITESPACE)
# message(STATUS "Using macOS sysroot: ${XCODE_SDK_ROOT}")

execute_process(COMMAND xcodebuild -sdk ${XCODE_SDK_ROOT} -version SDKVersion OUTPUT_VARIABLE XCODE_SDK_VERSION ERROR_QUIET OUTPUT_STRIP_TRAILING_WHITESPACE)

execute_process(COMMAND xcrun -sdk ${XCODE_SDK_ROOT} -find clang OUTPUT_VARIABLE XCODE_C_COMPILER ERROR_QUIET OUTPUT_STRIP_TRAILING_WHITESPACE)
# message(STATUS "Using C compiler: ${XCODE_C_COMPILER}")

execute_process(COMMAND xcrun -sdk ${XCODE_SDK_ROOT} -find clang++ OUTPUT_VARIABLE XCODE_CXX_COMPILER ERROR_QUIET OUTPUT_STRIP_TRAILING_WHITESPACE)
# message(STATUS "Using CXX compiler: ${XCODE_CXX_COMPILER}")

set(UNIX TRUE CACHE BOOL "")
set(APPLE TRUE CACHE BOOL "")
set(IOS TRUE CACHE BOOL "")

set(CMAKE_SYSTEM_NAME Darwin CACHE INTERNAL "")
set(CMAKE_SYSTEM_VERSION ${XCODE_SDK_VERSION} CACHE INTERNAL "")
set(CMAKE_C_COMPILER ${XCODE_C_COMPILER})
set(CMAKE_CXX_COMPILER ${XCODE_CXX_COMPILER})
set(CMAKE_OSX_SYSROOT ${XCODE_SDK_ROOT} CACHE PATH "Sysroot used for MacOSX support")
set(CMAKE_OSX_ARCHITECTURES ${XCODE_OS_ARCH} CACHE string "Build architecture for MacOSX")
set(CMAKE_FIND_ROOT_PATH ${XCODE_SDK_ROOT} ${CMAKE_PREFIX_PATH} CACHE string "MacOSX find search path root")

if(XCODE_OS_ARCH MATCHES "((^|, )(arm64|arm64e|x86_64))+")
    set(CMAKE_C_SIZEOF_DATA_PTR 8)
    set(CMAKE_CXX_SIZEOF_DATA_PTR 8)
    message(STATUS "Using a data_ptr size of 8")
else()
    set(CMAKE_C_SIZEOF_DATA_PTR 4)
    set(CMAKE_CXX_SIZEOF_DATA_PTR 4)
    message(STATUS "Using a data_ptr size of 4")
endif()

set(CMAKE_FIND_FRAMEWORK FIRST)
set(CMAKE_SYSTEM_FRAMEWORK_PATH ${XCODE_SDK_ROOT}/System/Library/Frameworks)
