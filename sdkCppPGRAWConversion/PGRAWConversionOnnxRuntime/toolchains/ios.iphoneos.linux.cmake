set(CMAKE_THREAD_LIBS_INIT "-lpthread")
set(CMAKE_HAVE_THREADS_LIBRARY 1)
set(CMAKE_USE_WIN32_THREADS_INIT 0)
set(CMAKE_USE_PTHREADS_INIT 1)

set(CMAKE_SYSTEM_NAME Darwin)
set(CMAKE_SYSTEM_VERSION 11)
set(UNIX True)
set(APPLE True)
set(IOS True)

if(NOT ARCHS)
    set(ARCHS arm64)
endif()

set(IOS_SDK_ROOT $ENV{IOS_SDK_HOME})

set(CMAKE_C_COMPILER "${IOS_SDK_ROOT}/bin/arm-apple-darwin11-clang")
set(CMAKE_CXX_COMPILER "${IOS_SDK_ROOT}/bin/arm-apple-darwin11-clang++")
set(IOS_LIBTOOL "${IOS_SDK_ROOT}/bin/arm-apple-darwin11-libtool")
set(CMAKE_C_CREATE_STATIC_LIBRARY "${IOS_LIBTOOL} -static -o <TARGET> <LINK_FLAGS> <OBJECTS> ")
set(CMAKE_CXX_CREATE_STATIC_LIBRARY "${IOS_LIBTOOL} -static -o <TARGET> <LINK_FLAGS> <OBJECTS> ")

set(CMAKE_OSX_SYSROOT "${IOS_SDK_ROOT}/SDK/iPhoneOS9.3.sdk" CACHE PATH "Sysroot used for iOS support")
set(CMAKE_OSX_ARCHITECTURES ${ARCHS} CACHE STRING "Build architecture for iOS")

set(CMAKE_TRY_COMPILE_TARGET_TYPE STATIC_LIBRARY)
set(CMAKE_SHARED_LIBRARY_PREFIX "lib")
set(CMAKE_SHARED_LIBRARY_SUFFIX ".dylib")
set(CMAKE_SHARED_MODULE_PREFIX "lib")
set(CMAKE_SHARED_MODULE_SUFFIX ".so")
set(CMAKE_C_COMPILER_ABI ELF)
set(CMAKE_CXX_COMPILER_ABI ELF)
set(CMAKE_C_HAS_ISYSROOT 1)
set(CMAKE_CXX_HAS_ISYSROOT 1)
set(CMAKE_MODULE_EXISTS 1)
set(CMAKE_DL_LIBS "")
set(CMAKE_C_OSX_COMPATIBILITY_VERSION_FLAG "-compatibility_version ")
set(CMAKE_C_OSX_CURRENT_VERSION_FLAG "-current_version ")
set(CMAKE_CXX_OSX_COMPATIBILITY_VERSION_FLAG "${CMAKE_C_OSX_COMPATIBILITY_VERSION_FLAG}")
set(CMAKE_CXX_OSX_CURRENT_VERSION_FLAG "${CMAKE_C_OSX_CURRENT_VERSION_FLAG}")

# if(ARCHS MATCHES "((^|, )(arm64|arm64e|x86_64))+")
#     set(CMAKE_C_SIZEOF_DATA_PTR 8)
#     set(CMAKE_CXX_SIZEOF_DATA_PTR 8)
#     message(STATUS "Using a data_ptr size of 8")
# else()
#     set(CMAKE_C_SIZEOF_DATA_PTR 4)
#     set(CMAKE_CXX_SIZEOF_DATA_PTR 4)
#     message(STATUS "Using a data_ptr size of 4")
# endif()

# set(XCODE_IOS_PLATFORM_VERSION_FLAGS "-miphoneos-version-min=8.0")

set(CMAKE_IOS_DEVELOPER_ROOT "${IOS_SDK_ROOT}/bin")

set(CMAKE_FIND_ROOT_PATH ${CMAKE_IOS_DEVELOPER_ROOT} ${CMAKE_OSX_SYSROOT} ${CMAKE_PREFIX_PATH} CACHE STRING "iOS find search path root" FORCE)

set(CMAKE_FIND_FRAMEWORK FIRST)

set(CMAKE_SYSTEM_FRAMEWORK_PATH ${CMAKE_OSX_SYSROOT}/System/Library/Frameworks)

# set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM ONLY)
# set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
# set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
