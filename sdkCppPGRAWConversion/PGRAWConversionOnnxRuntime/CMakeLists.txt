# 指定CMake的最低版本为3.6，如果版本低于此要求，将发生致命错误
cmake_minimum_required(VERSION 3.6 FATAL_ERROR)

# 禁用编译过程中Warning打印
if(MSVC)
    add_compile_options(/W0) # 禁用所有警告
else()
    add_compile_options(-w) # GCC/Clang 禁用所有警告
    if(APPLE)
        set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,-w") # macOS 链接器忽略警告
        set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -Wl,-w")
    endif()
endif()

# 如果定义了CMAKE_TOOLCHAIN_FILE，设置库输出路径根目录，并查找指定的工具链文件
if(CMAKE_TOOLCHAIN_FILE)
    # 设置库输出的根路径，默认为构建目录
    set(LIBRARY_OUTPUT_PATH_ROOT ${CMAKE_BINARY_DIR} CACHE PATH "root for library output, set this to change where android libs are compiled to")
    # 获取工具链文件的名称
    get_filename_component(CMAKE_TOOLCHAIN_FILE_NAME ${CMAKE_TOOLCHAIN_FILE} NAME)
    # 查找工具链文件
    find_file(CMAKE_TOOLCHAIN_FILE ${CMAKE_TOOLCHAIN_FILE_NAME} PATHS ${CMAKE_SOURCE_DIR} NO_DEFAULT_PATH)
    # 输出工具链文件的状态信息
    message(STATUS "CMAKE_TOOLCHAIN_FILE: ${CMAKE_TOOLCHAIN_FILE}")
endif()

# 设置C++标准为C++11
set(CMAKE_CXX_STANDARD 11)

# 如果CMAKE_INSTALL_PREFIX未定义，设置默认的安装前缀路径
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
    # 设置安装目录，默认为构建目录下的install目录
    set(CMAKE_INSTALL_PREFIX "${CMAKE_BINARY_DIR}/install" CACHE PATH "Installation Directory")
endif()
# 输出安装前缀路径的状态信息
message(STATUS "CMAKE_INSTALL_PREFIX: ${CMAKE_INSTALL_PREFIX}")

# 定义项目名称和使用的编程语言
project(PGRAWConversion LANGUAGES C CXX ASM)

# 提供一个选项，用于启用或禁用OpenMP支持，默认为打开状态
option(ENABLE_OPENMP "OpenMP Support" ON)

# 提供一个选项，用于启用或禁用OPENGL支持，默认为打开状态
option(USE_OPENGL "Enable OpenGL support" ON)

# 强制设置WIN32选项为OFF，[TODO]：在Windows系统中需要更改此设置
set(WIN32 OFF CACHE BOOL "" FORCE)
# 强制设置构建共享库的选项为OFF
set(BUILD_SHARED_LIBS OFF CACHE BOOL "" FORCE)
# 根据平台和构建选项设置库文件后缀
if(WIN32 AND BUILD_SHARED_LIBS)
    set(LIB_SUFFIX "dll.a")
else()
    set(LIB_SUFFIX "a")
endif()

# 设置链接标志、编译标志和依赖项的源目录和二进制目录
set(PGRAWCVT_LINK_FLAGS ${PGRAWCVT_LINK_FLAGS})
set(PGRAWCVT_COMPILE_FLAGS ${PGRAWCVT_COMPILE_FLAGS})
# set(PGRAWCVT_DEPENDENCIES_SOURCE_DIR ${CMAKE_SOURCE_DIR}/depends)
set(PGRAWCVT_DEPENDENCIES_SOURCE_DIR $ENV{HOME}/Projects/depends-ORT)
# set(PGRAWCVT_DEPENDENCIES_BINARY_DIR ${CMAKE_BINARY_DIR}/depends)

# 如果未定义CMAKE_BUILD_TYPE，设置默认的构建类型为release
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE release CACHE STRING "Choose the type of build" FORCE)
endif()

# 定义一个宏，用于检查依赖项是否存在
macro(dependency_check)
    # 如果依赖项不存在，发出致命错误并提示安装依赖项
    if(NOT EXISTS ${ARGV0})
        message(FATAL_ERROR "Dependency missing, run \"./scripts/depends_setup.py install ${ARGV1}\" with correct python interpreter to install ${ARGV2}.")
    endif()
endmacro(dependency_check)

# 如果启用了OpenMP，设置相关的编译和链接标志
if(ENABLE_OPENMP)
    # 输出启用OpenMP的状态信息
    message(STATUS "Enable OpenMP")
    # 根据不同的操作系统设置不同的OpenMP标志
    if(APPLE)
        # 对于iOS, watchOS, tvOS (自3.10.3版本起)
        if(IOS)
            set(OPENMP_INCLUDE_FLAG -I${PGRAWCVT_DEPENDENCIES_SOURCE_DIR}/libomp/ios/openmp.framework/Versions/A/Headers)
            set(OPENMP_LINK_FLAG ${PGRAWCVT_DEPENDENCIES_SOURCE_DIR}/libomp/ios/openmp.framework)
        else() # 对于macOS
            set(OPENMP_INCLUDE_FLAG -I${PGRAWCVT_DEPENDENCIES_SOURCE_DIR}/libomp/macos/openmp.framework/Versions/A/Headers)
            set(OPENMP_LINK_FLAG ${PGRAWCVT_DEPENDENCIES_SOURCE_DIR}/libomp/macos/openmp.framework)
        endif()
        # 检查OpenMP依赖项
        dependency_check(${OPENMP_LINK_FLAG} "omp" "OpenMP for Darwin")
    else() # 对于Linux, Android
        # 查找OpenMP包
        find_package(OpenMP)
        # 如果没有找到OpenMP目标，则设置线程库
        if(NOT TARGET OpenMP::OpenMP_CXX)
            find_package(Threads REQUIRED)
            add_library(OpenMP::OpenMP_CXX IMPORTED INTERFACE)
            set_property(TARGET OpenMP::OpenMP_CXX PROPERTY INTERFACE_COMPILE_OPTIONS ${OpenMP_CXX_FLAGS})
            set_property(TARGET OpenMP::OpenMP_CXX PROPERTY INTERFACE_LINK_LIBRARIES ${OpenMP_CXX_FLAGS} Threads::Threads)
        endif()
        set(OPENMP_LINK_FLAG OpenMP::OpenMP_CXX)
    endif()
    # 输出OpenMP的编译和链接标志的状态信息
    message(STATUS "OPENMP_INCLUDE_FLAG: ${OPENMP_INCLUDE_FLAG}")
    message(STATUS "OPENMP_LINK_FLAG: ${OPENMP_LINK_FLAG}")
endif()

# 输出操作系统检查的状态信息
message(STATUS "OS check")

# 对于Android和iOS，禁用共享库支持，并启用RTTI和异常处理
if(ANDROID OR IOS)
    set_property(GLOBAL PROPERTY TARGET_SUPPORTS_SHARED_LIBS FALSE)
    message(STATUS "Shared libraries are Disabled.")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -frtti -fexceptions")
    message(STATUS "RTTI and Exceptions are Enabled.")
endif()

# 根据不同的操作系统设置不同的操作系统和架构标志
if(IOS) # iOS
    set(PGRAWCVT_OS "ios")
    set(PGRAWCVT_ARCH ${ARCHS})
elseif(ANDROID) # Android
    set(PGRAWCVT_OS "android")
    list(APPEND PGRAWCVT_OS_LIBS log)
    if(${ANDROID_ABI} MATCHES "((^|, )(armeabi-v7a|arm64-v8a|x86_64))+")
        set(PGRAWCVT_ARCH ${ANDROID_ABI})
    else()
        message(FATAL_ERROR "Unsupported Android ABI")
    endif()
else() # 桌面系统
    if(APPLE) # macOS
        message(STATUS "macOS")
        set(PGRAWCVT_OS "Darwin")
        if(DEFINED ARCHS)
            set(PGRAWCVT_ARCH "${ARCHS}")
        elseif(${CMAKE_SYSTEM_PROCESSOR} MATCHES "arm64")
            set(PGRAWCVT_ARCH "arm64")
        else()
            set(PGRAWCVT_ARCH "x86_64")
        endif()
        message(STATUS "PGRAWCVT_ARCH: ${PGRAWCVT_ARCH}")
        set(CMAKE_OSX_DEPLOYMENT_TARGET "10.13" CACHE STRING "Minimum OS X deployment version" FORCE)
    elseif(WIN32) # Windows
        message(STATUS "Windows")
        set(PGRAWCVT_OS "Windows")
        set(PGRAWCVT_ARCH "x86_64")
        set(CMAKE_STATIC_LIBRARY_PREFIX "lib")
        set(CMAKE_STATIC_LIBRARY_SUFFIX ".a")
        add_compile_options(/utf-8 /MT)
    else() # Linux
        message(STATUS "Linux")
        set(PGRAWCVT_OS "Linux")
        set(PGRAWCVT_ARCH "x86_64")
    endif()
endif()
message(STATUS "PGRAWCVT_OS = ${PGRAWCVT_OS}, PGRAWCVT_ARCH = ${PGRAWCVT_ARCH}")

# 对于苹果系统，设置GPU计算相关的库
if(APPLE)
    enable_language(OBJCXX)
    find_library(METAL Metal REQUIRED)
    list(APPEND PGRAWCVT_OS_LIBS ${METAL})
    find_library(FOUNDATION Foundation REQUIRED)
    list(APPEND PGRAWCVT_OS_LIBS ${FOUNDATION})
    if(IOS)
        find_library(OPENGLES OpenGLES REQUIRED)
        list(APPEND PGRAWCVT_OS_LIBS ${OPENGLES})
    else()
        find_library(OPENGL OpenGL REQUIRED)
        list(APPEND PGRAWCVT_OS_LIBS ${OPENGL})
    endif()
    # find_library(GRAPHIC CoreGraphics) list(APPEND PGRAWCVT_OS_LIBS ${GRAPHIC})
elseif(ANDROID) # Android
    list(APPEND PGRAWCVT_OS_LIBS EGL)
    list(APPEND PGRAWCVT_OS_LIBS GLESv2)
endif()

# 添加子目录，这里是实际编译代码的地方
add_subdirectory(raw_conversion)
