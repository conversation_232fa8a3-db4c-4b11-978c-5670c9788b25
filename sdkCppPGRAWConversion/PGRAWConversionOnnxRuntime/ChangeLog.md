# PGRAWConversion Change Log

## Jul. 22, 2025

---

1. Mac OpenGL 内存泄漏修复
2. 取消 cpu 计算时的加速策略

---

## Jul. 21, 2025

---

1. cpu 4DLUT 计算优化，支持 8bit，16bit

---

## Jul. 17, 2025

---

1. 优化图像位深处理流程，避免8位图像强制转换为16位造成的内存消耗翻倍问题
2. 在SDK内部新增模板化处理函数，支持根据输入图像位深自动选择对应的处理路径
   - `triLinear_spatial_aware_template<T>` : 支持不同位深的3D LUT处理
   - `inferDenoise_template<T>` : 支持不同位深的去噪处理
3. 内存优化效果：
   - 8位图像：内存消耗减少约50%（避免转换为16位）
   - 16位图像：保持原有精度，无精度损失
4. 保持API接口向后兼容性

---

## Jun. 13, 2025

---

1. 支持 Windows 日志

---

## Jun. 10, 2025

---

1. 基于亮哥新提供的 OpenGL 调用方式无需在主程序中创建 OpenGL 窗口即可启用 OpenGL 加速 4DLUT 计算

---

## Mar. 10, 2025

---

1. 初始化接口变更

```cpp
   bool init(const OrtConfig *ort_config, const std::string &key, const std::string &user_code, const std::string &prod_code, bool enable_opengl = false); // 新增 enable_opengl 参数
```

2. 内存占用优化

---

## Nov. 28, 2024

---

1. 更新曝光模型
2. 更新调标准色模型
3. 更新调风格色模型
4. 更新分类器模型

## Oct. 27, 2024

---

1. 更新调标准色模型
2. 更新去杂色模型

## Oct. 13, 2024

---

1. 更新自动曝光模型
2. 更新去杂色模型

## Jun. 16, 2024

---

1. 发布功能包括 RAW 图提亮、去噪、调标准色
2. 调标准色仅支持常规标准肤色，暂不支持风格化标准肤色

---
