# PGRAWConversion

---

## 一、编译方法

---

### 1.1 安装 python 脚本依赖包

```bash
python -m pip install --user -r scripts/requirements.txt
```

### 1.2 安装 OpenMP

```bash
# For ubuntu 
sudo apt install libomp-dev
# For macOS
./scripts/depends_setup.py update
./scripts/depends_setup.py install omp
```

### 1.3 安装 App 依赖库

```bash
./scripts/depends_setup.py update
./scripts/depends_setup.py install args
./scripts/depends_setup.py install stbimg
```

### 1.4 编译各平台静态库

#### 1.4.1 编译当前 PC 平台

视平台不同，可生成 Darwin-x86_64 与 Linux-x86_64 架构的库与 app。

```bash
./scripts/compile.sh host
```

---

## 二、接口说明与调用方法

---

### 2.1 接口说明

使用步骤包括：
1）定义 ONNX 推理配置；
2）定义推理模型配置，可选用是否去噪、是否调标准色、调标准色模式，详见 `raw_conversion.hpp` 头文件；
3）使用图像转档 `init` 接口初始化对象；
4）使用图像转档 `run` 接口进行推理；

```cpp
// ONNX 配置 (cpu)
std::unique_ptr<OrtConfig> ort_conf_ptr(new OrtConfig()); // 创建一个指向 OrtConfig 对象的智能指针 ort_conf_ptr，并使用 new 运算符在堆上分配了一个新的 OrtConfig 对象，并将其地址传递给智能指针。
ort_conf_ptr->env = std::unique_ptr<Ort::Env>(new Ort::Env(ORT_LOGGING_LEVEL_ERROR, "LOG_TAG")); // 创建一个 Ort::Env 对象的智能指针，设定日志级别为 ORT_LOGGING_LEVEL_ERROR，标签为 "LOG_TAG"。
OrtMemoryInfo *p;
Ort::ThrowOnError(Ort::GetApi().CreateCpuMemoryInfo(OrtArenaAllocator, OrtMemTypeDefault, &p));
ort_conf_ptr->cpu_arena_mem = std::make_shared<Ort::MemoryInfo>(p);
ort_conf_ptr->session_opts.SetGraphOptimizationLevel(GraphOptimizationLevel::ORT_ENABLE_ALL); // 设置会话选项中的模型图优化等级为 ORT_ENABLE_ALL，即全部开启优化。
ort_conf_ptr->session_opts.SetLogSeverityLevel(ORT_LOGGING_LEVEL_ERROR);                      // 设置会话选项中的日志严重级别为 ORT_LOGGING_LEVEL_ERROR，即只输出错误级别的日志。
ort_conf_ptr->session_opts.SetIntraOpNumThreads(4); // 设置会话选项中的 intra 操作的线程数为 4，此参数仅在使用 CPU 时生效，在 GPU 下无效。

// 初始化
RAWConversion raw_conversion; // 创建图像转档类
bool status_init = raw_conversion.init(ort_conf_ptr.get(), key, user_code, prod_code); // TODO: 不启用 OpenGL 加速4DLUT计算过程
bool status_init = raw_conversion.init(ort_conf_ptr.get(), key, user_code, prod_code, true); // TODO: 启用 OpenGL 加速4DLUT计算过程

// 推理
RAWCVTConfig rawcvt_config(true, true, true, RAWCVTConfig::NORMAL, -1); // 推理配置
int adjust_mode; // 获取分类器结果
bool adjust_custom = false; // 是否启用支持客户已有JPG调标准色
bool status_exec = raw_conversion.run(rawcvt_config, srcImage, dstImage, 2, adjust_mode, adjust_custom); // TODO: run
```

### 2.2 调用示例

附完整Demo如下（包括启用了 OpenGL 加速4DLUT计算过程）：

```cpp
//
// PGRAWConversion
//
// Created by Tanqy on 2024/05/30.
// Copyright © 2024 Tanqy. All rights reserved.
//

// 定义STB图像处理库的实现部分
#define STB_IMAGE_IMPLEMENTATION
// 定义STB图像写入库的实现部分
#define STB_IMAGE_WRITE_IMPLEMENTATION

// 标准库引入
#include <fstream>  // 引入文件流库
#include <iostream> // 引入标准输入输出库

// 第三方库引入
#include "args.hxx"          // 引入args库，用于处理命令行参数
#include "stb_image.h"       // 引入STB图像读取库
#include "stb_image_write.h" // 引入STB图像写入库

// 项目内部头文件引入
#include "raw_conversion/raw_conversion/raw_conversion.hpp" // 引入RAWConversion类的声明

/**
 * 授权信息结构体
 *
 * 用于存储用户授权相关的信息，包括用户代码、平台和产品信息以及授权密钥
 */
struct AuthorizationInfo {
    std::string user_code;                                                   // 用户代码
    std::vector<std::pair<std::string, std::string>> platforms_and_products; // 平台和产品信息列表
    std::string authorized_key;                                              // 授权密钥
};

/**
 * 从指定文件路径中读取并解析授权信息
 *
 * @param file_path 待解析的文件路径
 * @param info 解析后得到的授权信息
 * @return 如果解析成功，则返回 true；否则返回 false
 */
bool parse_authorization_info(std::string file_path, AuthorizationInfo &info) {
    // 打开授权文件
    std::ifstream input(file_path);
    if (!input.is_open()) {
        std::cerr << "Error: failed to open file " << file_path << std::endl;
        return false;
    }

    // 逐行读取并解析文件内容
    std::string line;
    while (std::getline(input, line)) {
        // 解析用户代码
        if (line.find("USER CODE:") != std::string::npos) {
            std::getline(input, line);
            info.user_code = line.substr();
        }
        // 解析平台和产品信息
        else if (!line.empty() && line.find("PLATFORM:") != std::string::npos) {
            std::string platform = line.substr(10);
            std::getline(input, line);
            if (line.find("PRODUCTS:") == std::string::npos) {
                std::cerr << "Error: invalid format" << std::endl;
                return false;
            }
            std::getline(input, line);
            std::string products = line.substr();
            info.platforms_and_products.emplace_back(platform, products);
        }
        // 解析授权密钥
        else if (!line.empty() && line.find("AUTHORIZED KEY:") != std::string::npos) {
            std::getline(input, line);
            info.authorized_key = line.substr();
        }
    }

    return true;
}

namespace pgrawcvt { // 定义命名空间pgrawcvt

/**
 * 主函数 - 实现RAW图像转换的核心功能
 *
 * @param argc 命令行参数数量
 * @param argv 命令行参数数组
 * @return 程序执行状态码，0表示成功，非0表示失败
 */
int main(int argc, char **argv) {
    // 设置环境变量，控制日志输出级别
#if defined _WIN32 || defined __CYGWIN__
    _putenv_s("PGRAWCVT_CPP_MIN_VLOG_LEVEL", "1"); // 开启日志信息输出
    // _putenv_s("PGRAWCVT_CPP_DEBUG_DIR", "./");     // 开启中间图像输出
#else
    setenv("PGRAWCVT_CPP_MIN_VLOG_LEVEL", "1", 1); // 开启日志信息输出
    setenv("PGRAWCVT_CPP_DEBUG_DIR", "./", 1);     // 开启中间图像输出
#endif

    // 读取并验证授权信息
    AuthorizationInfo info;
    if (!parse_authorization_info("<EMAIL>", info)) {
        return 1;
    }
    std::string key = info.authorized_key;
    std::string user_code = info.user_code;
    std::string prod_code = info.platforms_and_products.front().second; // 所有平台使用相同的产品代码

    // 创建命令行参数解析器
    args::ArgumentParser parser("PinGuo RAWCVT", "");
    args::HelpFlag help(parser, "help", "Display this help menu", {'h', "help"});
    args::ValueFlag<std::string> input_path(parser, "image path", "Image file path", {"input"}, "");
    args::ValueFlag<std::string> output_path(parser, "output path", "Output file path", {"output"}, "");

    // 解析命令行参数
    try {
        parser.ParseCLI(argc, static_cast<const char *const *>(argv));
    } catch (const args::Help &) {
        std::cout << parser;
        return 0;
    } catch (const args::ParseError &e) {
        std::cerr << e.what() << std::endl;
        std::cerr << parser;
        return -1;
    } catch (const args::ValidationError &e) {
        std::cerr << e.what() << std::endl;
        std::cerr << parser;
        return -1;
    }

    // 配置ONNX运行环境
    std::unique_ptr<OrtConfig> ort_conf_ptr(new OrtConfig());                                        // 创建OrtConfig对象的智能指针
    ort_conf_ptr->env = std::unique_ptr<Ort::Env>(new Ort::Env(ORT_LOGGING_LEVEL_ERROR, "LOG_TAG")); // 设置ONNX环境

    // 创建CPU内存信息
    OrtMemoryInfo *p;
    Ort::ThrowOnError(Ort::GetApi().CreateCpuMemoryInfo(OrtArenaAllocator, OrtMemTypeDefault, &p));
    ort_conf_ptr->cpu_arena_mem = std::make_shared<Ort::MemoryInfo>(p);

    // 配置ONNX会话选项
    ort_conf_ptr->session_opts.SetGraphOptimizationLevel(GraphOptimizationLevel::ORT_ENABLE_ALL); // 启用所有图优化
    ort_conf_ptr->session_opts.SetLogSeverityLevel(ORT_LOGGING_LEVEL_ERROR);                      // 设置日志级别
    ort_conf_ptr->session_opts.SetIntraOpNumThreads(4);                                           // 设置线程数

#define USE_CUDA 1

    // 配置ONNX执行设备
    std::string ep_name = "cpu"; // 默认使用CPU
    std::string device_id = "0";

    if (ep_name == "cpu") {
        // CPU设备配置（如需特殊配置可在此处添加）
    } else if (ep_name == "cuda") {
        // GPU设备配置
#if USE_CUDA == 1
        // 创建并配置CUDA提供程序选项
        OrtCUDAProviderOptionsV2 *options;
        Ort::ThrowOnError(Ort::GetApi().CreateCUDAProviderOptions(&options));
        std::vector<const char *> keys{"device_id"};
        std::vector<const char *> values{"0"};
        Ort::ThrowOnError(Ort::GetApi().UpdateCUDAProviderOptions(options, keys.data(), values.data(), keys.size()));
        ort_conf_ptr->session_opts.AppendExecutionProvider_CUDA_V2(*options);
        Ort::GetApi().ReleaseCUDAProviderOptions(options);
#endif
    }

    // 读取输入图像
    cv::Mat srcImage = cv::imread(args::get(input_path).c_str(), cv::IMREAD_UNCHANGED);

    // ************************************** RAWConversion初始化 ****************************************
    // TODO: 创建图像转档类实例
    RAWConversion raw_conversion;

    // 记录初始化开始时间
    auto fl_init_tic = std::chrono::system_clock::now();

    // TODO: 初始化RAWConversion
    bool enable_opengl = true; // 启用OpenGL加速4DLUT计算过程
    bool status_init = raw_conversion.init(ort_conf_ptr.get(), key, user_code, prod_code, enable_opengl);

    // TODO: 配置RAWConversion参数
    RAWCVTConfig rawcvt_config(true, false, true, RAWCVTConfig::AUTO, -1);

    // 检查初始化状态
    if (status_init == false) {
        fprintf(stderr, "Failed to init RAWConversion.\n");
        return -1;
    }

    // 计算并输出初始化耗时
    auto fl_init_toc = std::chrono::system_clock::now();
    auto fl_init_use = std::chrono::duration_cast<std::chrono::milliseconds>(fl_init_toc - fl_init_tic);
    std::cout << "Time of init: " << fl_init_use.count() << " ms; " << std::endl;
    // -----------------------------------------------------------------------------------------------------

    // ************************************** RAWConversion执行 ****************************************
    cv::Mat dstImage;
    for (int i = 0; i < 1; i++) {
        // 记录执行开始时间
        auto fl_exec_tic = std::chrono::system_clock::now();

        // TODO: 执行RAW转换
        int adjust_mode;                                                                                         // 获取分类器结果
        bool adjust_custom = false;                                                                              // 是否启用支持客户已有JPG调标准色
        bool status_exec = raw_conversion.run(rawcvt_config, srcImage, dstImage, 2, adjust_mode, adjust_custom); // 执行转换

        // 检查执行状态
        if (status_exec == false) {
            fprintf(stderr, "Failed to exec RAWConversion.\n");
            return -1;
        }

        // 计算并输出执行耗时
        auto fl_exec_toc = std::chrono::system_clock::now();
        auto fl_exec_use = std::chrono::duration_cast<std::chrono::milliseconds>(fl_exec_toc - fl_exec_tic);
        std::cout << "Time of exec: " << fl_exec_use.count() << " ms, adjust_mode is " << adjust_mode << "." << std::endl;
    }
    // -----------------------------------------------------------------------------------------------------

    // 处理输出图像并保存
    if (dstImage.depth() == CV_16U) {
        cv::normalize(dstImage, dstImage, 0, 255, cv::NORM_MINMAX, CV_8U); // 如果是16位图像，就归一化到8位
    }
    // 保存结果图像（JPEG格式，最高质量）
    cv::imwrite(args::get(output_path).c_str(), dstImage, {cv::IMWRITE_JPEG_QUALITY, 100});
    return 0;
}

} // namespace pgrawcvt

int main(int argc, char **argv) { return pgrawcvt::main(argc, argv); }

---

## 三、场景用法说明

---

### 3.1 功能配置参数

`RAWCVTConfig` 结构体包含以下主要配置参数：

```cpp
struct RAWCVTConfig {
    bool enable_raw_lut;    // 是否启用自动曝光调亮（仅适用于16位数据）
    bool enable_denoise;    // 是否启用去噪模型
    bool enable_std_lut;    // 是否启用自动调色模型
    enum adjust_type {
        AUTO,               // 自动选择调色模式（系统自动判断使用标准或风格）
        NORMAL,             // 标准调色模式
        STYLE               // 风格调色模式
    } adjustType;
    int n_threads;          // 线程数量（-1表示使用所有可用线程）
};
```

### 3.2 不同数据类型的处理能力

#### 3.2.1 16位数据处理（完整流程）

**适用场景：** RAW图像数据或高质量16位图像
**支持功能：** 自动曝光 + 去噪 + 自动调色

```cpp
// 16位数据完整处理示例
cv::Mat src_image_16bit; // 16位输入图像
cv::Mat dst_image_16bit; // 16位输出图像

// 配置：启用所有功能
RAWCVTConfig config_16bit(
    true,                    // enable_raw_lut: 启用自动曝光
    true,                    // enable_denoise: 启用去噪
    true,                    // enable_std_lut: 启用自动调色
    RAWCVTConfig::AUTO,      // adjustType: 自动选择调色模式
    -1                       // n_threads: 使用所有线程
);

int adjust_mode;
bool status = raw_conversion.run(config_16bit, src_image_16bit, dst_image_16bit, 2, adjust_mode);
```

#### 3.2.2 8位数据处理（调色专用）

**适用场景：** JPEG图像或已处理的8位图像
**支持功能：** 去噪 + 自动调色（不支持自动曝光）

```cpp
// 8位数据处理示例
cv::Mat src_image_8bit;  // 8位输入图像
cv::Mat dst_image_8bit;  // 8位输出图像

// 配置：8位数据不启用自动曝光
RAWCVTConfig config_8bit(
    false,                   // enable_raw_lut: 禁用自动曝光（8位数据不支持）
    true,                    // enable_denoise: 启用去噪
    true,                    // enable_std_lut: 启用自动调色
    RAWCVTConfig::NORMAL,    // adjustType: 标准调色模式
    -1                       // n_threads: 使用所有线程
);

int adjust_mode;
bool status = raw_conversion.run(config_8bit, src_image_8bit, dst_image_8bit, 2, adjust_mode);
```

### 3.3 调色模式详解

#### 3.3.1 自动模式（AUTO）

**特点：** 系统自动分析图像内容，智能选择标准或风格调色
**适用场景：** 不确定图像风格，希望系统自动判断

```cpp
RAWCVTConfig config_auto(true, true, true, RAWCVTConfig::AUTO, -1);
int adjust_mode; // 返回值：0=标准模式，1=风格模式
bool status = raw_conversion.run(config_auto, srcImage, dstImage, 2, adjust_mode);
```

#### 3.3.2 标准模式（NORMAL）

**特点：** 还原图像自然色彩，适合日常摄影
**适用场景：** 人像、风景、纪实摄影

```cpp
RAWCVTConfig config_normal(true, true, true, RAWCVTConfig::NORMAL, -1);
int adjust_mode = 0; // 固定返回0（标准模式）
bool status = raw_conversion.run(config_normal, srcImage, dstImage, 2, adjust_mode);
```

#### 3.3.3 风格模式（STYLE）

**特点：** 保留图像艺术效果，色彩更加鲜明
**适用场景：** 艺术摄影、创意图像处理

```cpp
RAWCVTConfig config_style(true, true, true, RAWCVTConfig::STYLE, -1);
int adjust_mode = 1; // 固定返回1（风格模式）
bool status = raw_conversion.run(config_style, srcImage, dstImage, 2, adjust_mode);
```

### 3.4 常见使用场景

#### 3.4.1 RAW图像完整处理

```cpp
// 适用于相机RAW数据的完整处理流程
RAWCVTConfig raw_config(true, true, true, RAWCVTConfig::AUTO, -1);
bool status = raw_conversion.run(raw_config, raw_image_16bit, result_image, 2, adjust_mode, false);
```

#### 3.4.2 JPEG图像增强

```cpp
// 适用于JPEG图像的去噪和调色增强
RAWCVTConfig jpeg_config(false, true, true, RAWCVTConfig::NORMAL, -1);
bool status = raw_conversion.run(jpeg_config, jpeg_image_8bit, enhanced_image, 2, adjust_mode, true);
```

#### 3.4.3 仅去噪处理

```cpp
// 仅进行去噪处理，不进行调色
RAWCVTConfig denoise_config(false, true, false, RAWCVTConfig::AUTO, -1);
bool status = raw_conversion.run(denoise_config, noisy_image, denoised_image, 2, adjust_mode);
```

#### 3.4.4 仅调色处理

```cpp
// 仅进行调色处理，不进行去噪
RAWCVTConfig color_config(false, false, true, RAWCVTConfig::STYLE, -1);
bool status = raw_conversion.run(color_config, original_image, colored_image, 2, adjust_mode);
```

### 3.5 性能优化建议

#### 3.5.1 内存优化

* **8位数据处理：** 相比16位数据，内存消耗减少约50%
* **16位数据处理：** 保持原始精度，无精度损失

#### 3.5.2 线程配置

```cpp
// 根据设备性能配置线程数
int optimal_threads = std::thread::hardware_concurrency();
RAWCVTConfig config(true, true, true, RAWCVTConfig::AUTO, optimal_threads);
```

#### 3.5.3 OpenGL加速

```cpp
// 启用OpenGL加速以提升4DLUT计算性能
bool enable_opengl = true;
bool status_init = raw_conversion.init(ort_conf_ptr.get(), key, user_code, prod_code, enable_opengl);
```

---

## 四、注意事项

与之前未使用 OpenGL 时 SDK 的唯一区别

```cpp
    // 之前 SDK 的初始化函数
    bool status_init = raw_conversion.init(ort_conf_ptr.get(), key, user_code, prod_code);
```

```cpp
    // 新 SDK 的初始化函数, 新增 enable_opengl 参数但已被赋默认值为 false
    enable_opengl = true; // 启用OpenGL加速4DLUT计算过程
    bool status_init = raw_conversion.init(ort_conf_ptr.get(), key, user_code, prod_code, enable_opengl);
```
