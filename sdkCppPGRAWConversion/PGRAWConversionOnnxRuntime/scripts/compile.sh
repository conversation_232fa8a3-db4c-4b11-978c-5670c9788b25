#!/usr/bin/env bash

build_host() {
    HOST_INFO=$(uname -s)-$(uname -m)
    rm -rf build-output/${HOST_INFO}
    mkdir -p build-output/${HOST_INFO}
    pushd build-output/${HOST_INFO}
    CMAKE_ARGS=()
    CMAKE_ARGS+=("-DCMAKE_BUILD_TYPE=Release")
    CMAKE_ARGS+=("-DCMAKE_INSTALL_PREFIX=../install/${HOST_INFO}")
    CMAKE_ARGS+=("-DCMAKE_C_COMPILER=clang")
    CMAKE_ARGS+=("-DCMAKE_CXX_COMPILER=clang++")
    CMAKE_ARGS+=("-DCMAKE_C_FLAGS=-pthread")
    CMAKE_ARGS+=("-DCMAKE_CXX_FLAGS=-pthread")
    CMAKE_ARGS+=($@)
    cmake ../.. "${CMAKE_ARGS[@]}"
    cmake --build . --target install -- "-j${BUILD_JOBS}"
    popd
}

build_coverage() {
    if [ -z "${LLVM_PROFDATA_PATH}" ]; then
        echo "LLVM_PROFDATA_PATH not set."
        exit 1
    fi

    if [ -z "${LLVM_COV_PATH}" ]; then
        echo "LLVM_COV_PATH not set."
        exit 1
    fi
    OUTPUT_DIR=$(uname -s)-$(uname -m)-covr
    COVERAGE_EXE=./build-output/install/${OUTPUT_DIR}/bin/alpha_matting
    rm -rf build-output/${OUTPUT_DIR}
    mkdir -p build-output/${OUTPUT_DIR}
    pushd build-output/${OUTPUT_DIR}
    CMAKE_ARGS=()
    CMAKE_ARGS+=("-DCMAKE_BUILD_TYPE=Release")
    CMAKE_ARGS+=("-DCMAKE_INSTALL_PREFIX=../install/${OUTPUT_DIR}")
    CMAKE_ARGS+=("-DCMAKE_C_COMPILER=clang")
    CMAKE_ARGS+=("-DCMAKE_CXX_COMPILER=clang++")
    CMAKE_ARGS+=("-DCMAKE_C_FLAGS=-pthread")
    CMAKE_ARGS+=("-DCMAKE_CXX_FLAGS=-libstdc++")
    CMAKE_ARGS+=("-DENABLE_COVERAGE=ON")
    CMAKE_ARGS+=($@)
    cmake ../.. "${CMAKE_ARGS[@]}"
    cmake --build . --target install -- "-j${BUILD_JOBS}"
    popd
    LLVM_PROFILE_FILE=llvm_coverage.profraw ${COVERAGE_EXE}
    ${LLVM_PROFDATA_PATH} merge -sparse llvm_coverage.profraw -output llvm_coverage.profdata
    ${LLVM_COV_PATH} show ${COVERAGE_EXE} -instr-profile=llvm_coverage.profdata
    ${LLVM_COV_PATH} report ${COVERAGE_EXE} -instr-profile=llvm_coverage.profdata
    rm -rf llvm_coverage.profraw
    rm -rf llvm_coverage.profdata
}

build_android() {
    if [ -z "${ANDROID_NDK}" ]; then
        echo "ANDROID_NDK not set."
        exit 1
    fi

    if [ ! -f "${ANDROID_NDK}/build/cmake/android.toolchain.cmake" ]; then
        echo "Android CMake toolchain file not exist."
        exit 1
    fi
    rm -rf build-output/android-${1}
    mkdir -p build-output/android-${1}
    pushd build-output/android-${1}
    CMAKE_ARGS=()
    CMAKE_ARGS+=("-DCMAKE_BUILD_TYPE=Release")
    CMAKE_ARGS+=("-DCMAKE_INSTALL_PREFIX=../install/android-${1}")
    CMAKE_ARGS+=("-DCMAKE_TOOLCHAIN_FILE=${ANDROID_NDK}/build/cmake/android.toolchain.cmake")
    CMAKE_ARGS+=("-DANDROID_TOOLCHAIN=clang")
    CMAKE_ARGS+=("-DANDROID_STL=c++_static")
    CMAKE_ARGS+=("-DANDROID_ABI=${1}")
    CMAKE_ARGS+=("-DANDROID_ARM_NEON=ON")
    # CMAKE_ARGS+=("-DANDROID_CPP_FEATURES=rtti exceptions")
    CMAKE_ARGS+=("-DANDROID_PIE=ON")
    CMAKE_ARGS+=("-DANDROID_NATIVE_API_LEVEL=${2}")
    CMAKE_ARGS+=("-DANDROID_PLATFORM=android-${3}")
    cmake ../.. "${CMAKE_ARGS[@]}"
    cmake --build . --target install -- "-j${BUILD_JOBS}"
    popd
    # python3 ./scripts/remote_runner.py --cfg ./scripts/remote_devices/Android_usb.yaml --exe-dir build-output/install/android-${1}/test
}

build_ios_on_linux() {
    if [ -z "${IOS_SDK_HOME}" ]; then
        echo "IOS_SDK_HOME not set."
        exit 1
    fi

    if [ ! -f "${IOS_SDK_HOME}/bin/arm-apple-darwin11-clang" ]; then
        echo "C++ compiler arm-apple-darwin11-clang not exist."
        exit 1
    fi
    rm -rf build-output/ios-${1}-linux
    mkdir -p build-output/ios-${1}-linux
    pushd build-output/ios-${1}-linux
    CMAKE_ARGS=()
    CMAKE_ARGS+=("-DCMAKE_BUILD_TYPE=Release")
    CMAKE_ARGS+=("-DCMAKE_INSTALL_PREFIX=../install/ios-${1}-linux")
    CMAKE_ARGS+=("-DCMAKE_TOOLCHAIN_FILE=$(pwd)/../../toolchains/ios.iphoneos.linux.cmake")
    CMAKE_ARGS+=("-DARCHS=${1}")
    cmake ../.. "${CMAKE_ARGS[@]}"
    cmake --build . --target install -- "-j${BUILD_JOBS}"
    popd
    # python3 ./scripts/remote_runner.py --cfg ./scripts/remote_devices/iPhone6_lan.yaml --exe-dir build-output/install/ios-${1}-linux/test
}

build_ios_on_macos() {
    rm -rf build-output/ios-${2}
    mkdir -p build-output/ios-${2}
    pushd build-output/ios-${2}
    CMAKE_ARGS=()
    CMAKE_ARGS+=("-DCMAKE_BUILD_TYPE=Release")
    CMAKE_ARGS+=("-DCMAKE_INSTALL_PREFIX=../install/ios-${2}")
    CMAKE_ARGS+=("-DCMAKE_TOOLCHAIN_FILE=$(pwd)/../../toolchains/ios.all.macos.cmake")
    CMAKE_ARGS+=("-DCMAKE_MACOSX_BUNDLE=0")
    CMAKE_ARGS+=("-DDEPLOYMENT_TARGET=8.0")
    CMAKE_ARGS+=("-DENABLE_ARC=0")
    CMAKE_ARGS+=("-DENABLE_BITCODE=1")
    CMAKE_ARGS+=("-DENABLE_VISIBILITY=0")
    CMAKE_ARGS+=("-DPLATFORM=${1}")
    CMAKE_ARGS+=("-DARCHS=${2}")
    cmake ../.. "${CMAKE_ARGS[@]}"
    cmake --build . --target install -- "-j${BUILD_JOBS}"
    popd
    # python3 ./scripts/remote_runner.py --cfg ./scripts/remote_devices/iPhone6_lan.yaml --exe-dir build-output/install/ios-${2}/test
}

build_with_clang_on_linux() {
    rm -rf build-output/linux
    mkdir -p build-output/linux
    pushd build-output/linux
    CMAKE_ARGS=()
    CMAKE_ARGS+=("-DCMAKE_BUILD_TYPE=Release")
    CMAKE_ARGS+=("-DCMAKE_C_COMPILER=clang")
    CMAKE_ARGS+=("-DCMAKE_CXX_COMPILER=clang++")
    CMAKE_ARGS+=("-DCMAKE_C_FLAGS=-pthread")
    CMAKE_ARGS+=("-DCMAKE_CXX_FLAGS=-pthread")
    cmake ../.. "${CMAKE_ARGS[@]}"
    cmake --build . --target install -- "-j${BUILD_JOBS}"
    popd
}

build_brcm() {
    # For Docker you may need:
    # apt-get install -y libc6-i386 lib32stdc++6 lib32z1 lib32ncurses5 lib32z1 libc6-dev-i386
    if [ -z "${BRCM_SDK_HOME}" ]; then
        echo "BRCM_SDK_HOME not set."
        exit 1
    fi
    if [ ! -f "${BRCM_SDK_HOME}/cmake/brcm.aarch64.toolchain.cmake" ]; then
        echo "Broadcom CMake toolchain file not exist."
        exit 1
    fi
    export LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:${BRCM_SDK_HOME}/lib
    rm -rf build-output/brcm
    mkdir -p build-output/brcm
    pushd build-output/brcm
    CMAKE_ARGS=()
    CMAKE_ARGS+=("-DCMAKE_BUILD_TYPE=Release")
    CMAKE_ARGS+=("-DCMAKE_INSTALL_PREFIX=../install/brcm-aarch64")
    CMAKE_ARGS+=("-DCMAKE_TOOLCHAIN_FILE=${BRCM_SDK_HOME}/cmake/brcm.aarch64.toolchain.cmake")
    CMAKE_ARGS+=("-DCMAKE_EXE_LINKER_FLAGS=-static -s")
    cmake ../.. "${CMAKE_ARGS[@]}"
    cmake --build . --target install -- "-j${BUILD_JOBS}"
    popd
}

build_macos_on_macos() {
    rm -rf build-output/Darwin-${2}
    mkdir -p build-output/Darwin-${2}
    pushd build-output/Darwin-${2}
    CMAKE_ARGS=()
    CMAKE_ARGS+=("-DCMAKE_BUILD_TYPE=Release")
    CMAKE_ARGS+=("-DCMAKE_INSTALL_PREFIX=../install/Darwin-${2}")
    CMAKE_ARGS+=("-DCMAKE_TOOLCHAIN_FILE=$(pwd)/../../toolchains/macos.all.macos.cmake")
    CMAKE_ARGS+=("-DCMAKE_MACOSX_BUNDLE=0")
    CMAKE_ARGS+=("-DENABLE_ARC=0")
    CMAKE_ARGS+=("-DENABLE_BITCODE=0")
    CMAKE_ARGS+=("-DENABLE_VISIBILITY=0")
    CMAKE_ARGS+=("-DPLATFORM=${1}")
    CMAKE_ARGS+=("-DARCHS=${2}")
    cmake ../.. "${CMAKE_ARGS[@]}"
    cmake --build . --target install -- "-j${BUILD_JOBS}"
    popd
}

set -e

if [ $(uname) == "Darwin" ]; then
    export BUILD_JOBS=$(sysctl -n hw.ncpu)
else
    export BUILD_JOBS=$(nproc)
fi

TARGET_OS=$(echo ${1} | awk '{print tolower($0)}')

if [ "${TARGET_OS}" == "host" ]; then
    build_host
    if [ "$(uname -s)" == "Darwin" ]; then
        build_macos_on_macos MAC x86_64
        build_macos_on_macos MAC_ARM64 arm64
    else
        build_host
    fi
elif [ "${TARGET_OS}" == "android" ]; then
    build_android armeabi-v7a 19 19
    build_android arm64-v8a 21 21
    # build_android x86 19 19
    # build_android x86_64 21 21
elif [ "${TARGET_OS}" == "ios" ]; then
    if [ "$(uname -s)" == "Darwin" ]; then
        build_ios_on_macos OS armv7
        build_ios_on_macos OS armv7s
        build_ios_on_macos OS arm64
        build_ios_on_macos OS arm64e
        # build_ios_on_macos SIMULATOR i386
        build_ios_on_macos SIMULATOR64 x86_64
    else
        # build_ios_on_linux armv7
        build_ios_on_linux armv7s
        build_ios_on_linux arm64
    fi
elif [ "${TARGET_OS}" == "brcm" ]; then
    build_brcm
elif [ "${TARGET_OS}" == "coverage" ]; then
    build_coverage
else
    echo "PGFaceLiquify compilation assistant"
    echo "Usage: ${0} <Target OS>"
    echo "Target OS: host | android | ios | brcm"
fi
