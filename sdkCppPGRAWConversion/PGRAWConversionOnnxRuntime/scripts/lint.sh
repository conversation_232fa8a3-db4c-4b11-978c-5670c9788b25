#!/usr/bin/env bash

pgdnncpplint() {
    cpplint --linelength=95 --root=$(pwd) $(find ${1} -name "*.hpp" -or -name "*.cpp")
}

pgdnnpylint() {
    pycodestyle --max-line-length=95 $(find ${1} -name "*.py")
}

pgdnncppfmt() {
    clang-format -i -style="{ \
                    BasedOnStyle: google, \
                    IndentWidth: 4, \
                    ColumnLimit: 95, \
                    AccessModifierOffset: -3, \
                    PointerAlignment: Left, \
                    DerivePointerAlignment: false, \
                    BinPackParameters: false, \
                    AllowShortFunctionsOnASingleLine: true, \
                    AllowShortCaseLabelsOnASingleLine: true, \
                    AllowShortIfStatementsOnASingleLine: true, \
                    AllowShortLoopsOnASingleLine: true, \
                    AllowShortBlocksOnASingleLine: true}" \
                    $(find ${1} -name "*.hpp" -or -name "*.cpp")
}

pgdnnpyfmt() {
    yapf -i --style="{ \
        based_on_style: pep8, \
        column_limit: 95, \
        continuation_align_style: space}" $(find ${1} -name "*.py")
}

set -e
echo "PGFaceLiquify C++ Lint:"
pgdnncpplint face_liquify

#echo "PGFaceLiquify Python Lint:"
#pgdnnpylint python
#pgdnnpylint tester
