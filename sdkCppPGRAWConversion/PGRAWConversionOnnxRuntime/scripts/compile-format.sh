#!/usr/bin/env bash

# 设置错误时退出
set -e

# 全局变量：设置构建作业数量
# 在 macOS 上使用 sysctl，在其他系统上使用 nproc
export BUILD_JOBS=$([ "$(uname)" == "Darwin" ] && sysctl -n hw.ncpu || nproc)

# 通用构建函数
# 参数:
#   $1: 输出目录名
#   $2: 安装目录名
#   $3+: CMake 参数
build_generic() {
    local output_dir=$1
    local install_dir=$2
    shift 2
    rm -rf "build-output/${output_dir}"
    mkdir -p "build-output/${output_dir}"
    pushd "build-output/${output_dir}"
    echo -e "\n\n\n\033[0;31m执行构建命令: cmake ../.. $@\033[0m"
    cmake ../.. "$@"
    echo -e "\n\033[0;31m执行编译命令: cmake --build . --target install -- -j${BUILD_JOBS}\033[0m"
    cmake --build . --target install -- "-j${BUILD_JOBS}"
    popd
}

# 主机构建函数
# 参数:
#   $@: 额外的 CMake 参数
build_host() {
    local host_info="$(uname -s)-$(uname -m)"
    local cmake_args=(
        "-DCMAKE_BUILD_TYPE=Release"
        "-DCMAKE_INSTALL_PREFIX=../install/${host_info}"
        "-DCMAKE_C_COMPILER=clang"
        "-DCMAKE_CXX_COMPILER=clang++"
        "-DCMAKE_C_FLAGS=-pthread"
        "-DCMAKE_CXX_FLAGS=-pthread"
        "$@"
    )
    build_generic "${host_info}" "../install/${host_info}" "${cmake_args[@]}"
}

# 覆盖率构建函数
# 参数:
#   $@: 额外的 CMake 参数
build_coverage() {
    # 检查必要的环境变量是否设置
    [ -z "${LLVM_PROFDATA_PATH}" ] && {
        echo "LLVM_PROFDATA_PATH not set."
        exit 1
    }
    [ -z "${LLVM_COV_PATH}" ] && {
        echo "LLVM_COV_PATH not set."
        exit 1
    }

    local output_dir="$(uname -s)-$(uname -m)-covr"
    local coverage_exe="./build-output/install/${output_dir}/bin/alpha_matting"
    local cmake_args=(
        "-DCMAKE_BUILD_TYPE=Release"
        "-DCMAKE_INSTALL_PREFIX=../install/${output_dir}"
        "-DCMAKE_C_COMPILER=clang"
        "-DCMAKE_CXX_COMPILER=clang++"
        "-DCMAKE_C_FLAGS=-pthread"
        "-DCMAKE_CXX_FLAGS=-pthread"
        "-DENABLE_COVERAGE=ON"
        "$@"
    )
    build_generic "${output_dir}" "../install/${output_dir}" "${cmake_args[@]}"

    # 运行覆盖率分析
    echo "执行覆盖率分析命令: LLVM_PROFILE_FILE=llvm_coverage.profraw ${coverage_exe}"
    LLVM_PROFILE_FILE=llvm_coverage.profraw ${coverage_exe}
    echo "执行 profdata 合并命令: ${LLVM_PROFDATA_PATH} merge -sparse llvm_coverage.profraw -output llvm_coverage.profdata"
    ${LLVM_PROFDATA_PATH} merge -sparse llvm_coverage.profraw -output llvm_coverage.profdata
    echo "执行覆盖率显示命令: ${LLVM_COV_PATH} show ${coverage_exe} -instr-profile=llvm_coverage.profdata"
    ${LLVM_COV_PATH} show ${coverage_exe} -instr-profile=llvm_coverage.profdata
    echo "执行覆盖率报告命令: ${LLVM_COV_PATH} report ${coverage_exe} -instr-profile=llvm_coverage.profdata"
    ${LLVM_COV_PATH} report ${coverage_exe} -instr-profile=llvm_coverage.profdata
    rm -rf llvm_coverage.profraw llvm_coverage.profdata
}

# Android 构建函数
# 参数:
#   $1: ABI (e.g., armeabi-v7a, arm64-v8a)
#   $2: API 级别
#   $3: 平台版本
build_android() {
    # 检查必要的环境变量和文件
    [ -z "${ANDROID_NDK}" ] && {
        echo "ANDROID_NDK not set."
        exit 1
    }
    [ ! -f "${ANDROID_NDK}/build/cmake/android.toolchain.cmake" ] && {
        echo "Android CMake toolchain file not exist."
        exit 1
    }

    local abi=$1
    local api_level=$2
    local platform=$3
    local cmake_args=(
        "-DCMAKE_BUILD_TYPE=Release"
        "-DCMAKE_INSTALL_PREFIX=../install/android-${abi}"
        "-DCMAKE_TOOLCHAIN_FILE=${ANDROID_NDK}/build/cmake/android.toolchain.cmake"
        "-DANDROID_TOOLCHAIN=clang"
        "-DANDROID_STL=c++_static"
        "-DANDROID_ABI=${abi}"
        "-DANDROID_ARM_NEON=ON"
        "-DANDROID_PIE=ON"
        "-DANDROID_NATIVE_API_LEVEL=${api_level}"
        "-DANDROID_PLATFORM=android-${platform}"
    )
    build_generic "android-${abi}" "../install/android-${abi}" "${cmake_args[@]}"
}

# iOS 在 Linux 上构建函数
# 参数:
#   $1: 架构 (e.g., armv7, arm64)
build_ios_on_linux() {
    # 检查必要的环境变量和文件
    [ -z "${IOS_SDK_HOME}" ] && {
        echo "IOS_SDK_HOME not set."
        exit 1
    }
    [ ! -f "${IOS_SDK_HOME}/bin/arm-apple-darwin11-clang" ] && {
        echo "C++ compiler arm-apple-darwin11-clang not exist."
        exit 1
    }

    local arch=$1
    local cmake_args=(
        "-DCMAKE_BUILD_TYPE=Release"
        "-DCMAKE_INSTALL_PREFIX=../install/ios-${arch}-linux"
        "-DCMAKE_TOOLCHAIN_FILE=$(pwd)/toolchains/ios.iphoneos.linux.cmake"
        "-DARCHS=${arch}"
    )
    build_generic "ios-${arch}-linux" "../install/ios-${arch}-linux" "${cmake_args[@]}"
}

# iOS 在 macOS 上构建函数
# 参数:
#   $1: 平台 (e.g., OS, SIMULATOR64)
#   $2: 架构 (e.g., armv7, arm64, x86_64)
build_ios_on_macos() {
    local platform=$1
    local arch=$2
    local cmake_args=(
        "-DCMAKE_BUILD_TYPE=Release"
        "-DCMAKE_INSTALL_PREFIX=../install/ios-${arch}"
        "-DCMAKE_TOOLCHAIN_FILE=$(pwd)/toolchains/ios.all.macos.cmake"
        "-DCMAKE_MACOSX_BUNDLE=0"
        "-DDEPLOYMENT_TARGET=8.0"
        "-DENABLE_ARC=0"
        "-DENABLE_BITCODE=0"
        "-DENABLE_VISIBILITY=0"
        "-DPLATFORM=${platform}"
        "-DARCHS=${arch}"
    )
    build_generic "ios-${arch}" "../install/ios-${arch}" "${cmake_args[@]}"
}

# BRCM 构建函数
build_brcm() {
    # 检查必要的环境变量和文件
    [ -z "${BRCM_SDK_HOME}" ] && {
        echo "BRCM_SDK_HOME not set."
        exit 1
    }
    [ ! -f "${BRCM_SDK_HOME}/cmake/brcm.aarch64.toolchain.cmake" ] && {
        echo "Broadcom CMake toolchain file not exist."
        exit 1
    }

    export LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:${BRCM_SDK_HOME}/lib
    local cmake_args=(
        "-DCMAKE_BUILD_TYPE=Release"
        "-DCMAKE_INSTALL_PREFIX=../install/brcm-aarch64"
        "-DCMAKE_TOOLCHAIN_FILE=${BRCM_SDK_HOME}/cmake/brcm.aarch64.toolchain.cmake"
        "-DCMAKE_EXE_LINKER_FLAGS=-static -s"
    )
    build_generic "brcm" "../install/brcm-aarch64" "${cmake_args[@]}"
}

# macOS 在 macOS 上构建函数
# 参数:
#   $1: 平台 (e.g., MAC, MAC_ARM64)
#   $2: 架构 (e.g., x86_64, arm64)
build_macos_on_macos() {
    local platform=$1
    local arch=$2
    local cmake_args=(
        "-DCMAKE_BUILD_TYPE=Release"
        "-DCMAKE_INSTALL_PREFIX=../install/Darwin-${arch}"
        "-DCMAKE_TOOLCHAIN_FILE=$(pwd)/toolchains/macos.all.macos.cmake"
        "-DCMAKE_MACOSX_BUNDLE=0"
        "-DENABLE_ARC=0"
        "-DENABLE_BITCODE=0"
        "-DENABLE_VISIBILITY=0"
        "-DPLATFORM=${platform}"
        "-DARCHS=${arch}"
    )
    build_generic "Darwin-${arch}" "../install/Darwin-${arch}" "${cmake_args[@]}"
}

# 主函数
# 参数:
#   $1: 目标操作系统
main() {
    local target_os=$(echo ${1} | awk '{print tolower($0)}')

    case "${target_os}" in
    "host")
        build_host
        if [ "$(uname -s)" == "Darwin" ]; then
            build_macos_on_macos MAC x86_64
            build_macos_on_macos MAC_ARM64 arm64
        else
            build_host
        fi
        ;;
    "android")
        build_android armeabi-v7a 19 19
        build_android arm64-v8a 21 21
        ;;
    "ios")
        if [ "$(uname -s)" == "Darwin" ]; then
            build_ios_on_macos OS armv7
            build_ios_on_macos OS armv7s
            build_ios_on_macos OS arm64
            build_ios_on_macos OS arm64e
            build_ios_on_macos SIMULATOR64 x86_64
        else
            build_ios_on_linux armv7s
            build_ios_on_linux arm64
        fi
        ;;
    "brcm")
        build_brcm
        ;;
    "coverage")
        build_coverage
        ;;
    *)
        echo "PGFaceLiquify compilation assistant"
        echo "Usage: ${0} <Target OS>"
        echo "Target OS: host | android | ios | brcm | coverage"
        ;;
    esac
}

# 执行主函数
main "$@"
