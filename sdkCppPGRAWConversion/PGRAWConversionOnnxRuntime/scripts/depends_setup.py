#!/usr/bin/env python

from __future__ import division
from __future__ import print_function
from __future__ import absolute_import

import os
import sh
import sys
import glob
import shutil
import requests
import easydict
import tempfile
import argparse
import distutils.sysconfig


def package_info(name, url, sha256sum):
    info = easydict.EasyDict()
    info.name = name
    info.url = url
    info.hash = sha256sum
    return info


g_pkgs = dict()
g_pkgs['proto'] = package_info(
    'protobuf',
    'https://www.sinsoul.com/data/protobuf_v3.7.1.zip',
    'f976a4cd3f1699b6d20c1e944ca1de6754777918320c719742e1674fcf247b7e')
# g_pkgs['flat'] = package_info(
#     'flatbuffers',
#     'https://www.sinsoul.com/data/flatbuffers_v1.10.0.zip',
#     'dd747bbf5754477ee50a67d65053deef01c91a158e8215a843945586a80d4997', False)
# g_pkgs['flat'] = package_info(
#     'flatbuffers',
#     'https://www.sinsoul.com/data/flatbuffers_v2.0.0.zip',
#     '7563c10d09b51e89c751d04db7236f2df07a5b720acfeb8fb3f60386b5d16d23', False)
g_pkgs['flat'] = package_info(
    'flatbuffers',
    'https://www.sinsoul.com/data/flatbuffers_v2.0.0_2.zip',
    'f495e6716fee93055112f6ca2418cd2b5299d304ff7a6be474dc6f76741e8fc1')
g_pkgs['gtest'] = package_info(
    'googletest',
    'https://www.sinsoul.com/data/googletest_v1.8.1.zip',
    '927827c183d01734cc5cfef85e0ff3f5a92ffe6188e0d18e909c5efebf28a0c7')
g_pkgs['args'] = package_info(
    'args',
    'https://www.sinsoul.com/data/args_v6.2.2.zip',
    '1a1c8846acd2d117843f6ab13518cac78bd0f8dcde8531603ac6f2115c9582d6')
g_pkgs['omp'] = package_info(
    'libomp',
    'https://www.sinsoul.com/data/libomp.zip',
    '6992cac578c178aedc02ff460c83f1d2e3ca0da6dbecf09256ce0ed0c9224ee8')
g_pkgs['stbimg'] = package_info(
    'stb_image',
    'https://www.sinsoul.com/data/stb_image.zip',
    '1e4d44d263789d77d461805d9463f96d291e577027fa97772adf74a701368399')
g_pkgs['half'] = package_info(
    'half_code',
    'https://www.sinsoul.com/data/half_code.zip',
    'fb76d1bbdc02ef3c6b929d84c1e3a332ec208adc3c6ad5d0db6474baded92047')
g_pkgs['tflite'] = package_info(
    'tflite',
    'https://www.sinsoul.com/data/tflite.zip',
    '187f9c7df12b521da43b515723048aff4867a1ac6956c2f622fb35f429c97b15')
g_pkgs['gemmlowp'] = package_info(
    'gemmlowp',
    'https://www.sinsoul.com/data/gemmlowp.zip',
    '87d2a18b9dd7e16b5f80e4b7340951cfd547b4c42618604f722e6770fd7ebdbf')
g_pkgs['neon2sse'] = package_info(
    'neon2sse',
    'https://www.sinsoul.com/data/neon2sse.zip',
    '10ff50dec21e559ae0659f61deaf9c347278f21d76ca16919b11efe7ed12709e')
g_pkgs['xtensor'] = package_info(
    'xtensor',
    'https://www.sinsoul.com/data/xtensor.zip',
    'ff4e03d3e3813f1f5a038b072b658eb2d12d7848e8fe9748ebe8c5eba2ba57ce')
g_pkgs['pnn'] = package_info(
    'pnn',
    'https://www.sinsoul.com/data/pnn.zip',
    'e3fe0934dd6aedd0f4e9aaafc4389c79d3928f93efd68287dab024f9d1f8c487')
g_pkgs['opencv'] = package_info(
    'opencv',
    'https://www.sinsoul.com/data/opencv.zip',
    'd070edd7ca73d108efae193d46231bc2a29ca6ef14a90a0201798b3af9bdad71')
# g_pkgs['opencv'] = package_info(
#     'opencv',
#     'https://www.sinsoul.com/data/opencv_v4.2.0.zip',
#     'ad6a256141efbafba1a7d5cac3b43a211fea2e6b7ad20923b41bc3c2cbe7b9d7')
g_pkgs['pybind11'] = package_info(
    'pybind11',
    'https://www.sinsoul.com/data/pybind11.zip',
    'edfefe01c9a3b46d5d1c6f639f24750de18a3bf62e0fadbeba296d02b40a9501')
g_pkgs['pgmatrix'] = package_info(
    'pgmatrix',
    'https://www.sinsoul.com/data/pgmatrix.zip',
    '6b94e183773736efc180c984b8999f874973c887e0023c1141963fd17d692b58')
g_pkgs['eigen3'] = package_info(
    'eigen3',
    'https://www.sinsoul.com/data/eigen3.zip',
    '79e514f35f99cc38237291e3320f7dfc0a393a401184bf775eb6cd48b5c1c4cb')
g_pkgs['pglf'] = package_info(
    'libPGLightFace',
    'https://www.sinsoul.com/data/libPGLightFace.zip',
    '203da6f793041740dcb57337f3021cb08b3086e029bc4b6829a5a864dd0d625f')
g_pkgs['pgbc'] = package_info(
    'libPGBlemishCure',
    'https://www.sinsoul.com/data/libPGBlemishCure.zip',
    '4d08c2bd9d75f6ababa9d38e7178d5aeb61a5d826ece8d7048c277a1a6b0d039')
g_pkgs['pggf'] = package_info(
    'libPGGuidedFilter',
    'https://www.sinsoul.com/data/libPGGuidedFilter.zip',
    '002f8a50603605994e3e1ec25ea403d14c8484f58b5e5ddc26fa0607ef845cd6')
g_pkgs['pgam'] = package_info(
    'libPGAlphaMatting',
    'https://www.sinsoul.com/data/libPGAlphaMatting.zip',
    '06b22ecd159393360b2b99b0131e0e27cb80d8cc68f7975a4e10598f0d951afb')
g_pkgs['pgdnn'] = package_info(
    'libPGDeepNeuralNetwork',
    'https://www.sinsoul.com/data/libPGDeepNeuralNetwork.zip',
    '8bf91612937dea1ab05930fd03b1cc293a39ed204c209433b51a972be59bbfbb')
g_pkgs['pgie'] = package_info(
    'libPGImageExtract',
    'https://www.sinsoul.com/data/libPGImageExtract.zip',
    'c1d6f12c26981e1bd29297b2f8b9622bb17528f1afbf246994bbfc49a98c4b0e')
g_pkgs['ssim'] = package_info(
    'ssim',
    'https://www.sinsoul.com/data/ssim.zip',
    'e3d1c07f45b113da924c730fac9452c27b64d8073627753fc7d1d1cee15cff65')
g_pkgs['pgas'] = package_info(
    'libPGAuthoritySystem',
    'https://www.sinsoul.com/data/libPGAuthoritySystem.zip',
    '6688e2814fcaefd4c443b198ef9643c292eec9ac3088991c1369e41727a6dca6')
g_pkgs['pgmb'] = package_info(
    'libPGMotionBlur',
    'https://www.sinsoul.com/data/libPGMotionBlur.zip',
    'e16f9adc3b066b552caf5ba5234353f706a7d064442dcad65db539c801e6ce86')
g_pkgs['pgdr'] = package_info(
    'libPGDeepRetouch',
    'https://www.sinsoul.com/data/libPGDeepRetouch.zip',
    '38c93d5a63d36c5a5e8a95f16faaf6f369f08a9ed8a0440e3a57af23a92a2d27')
g_pkgs['pgsm'] = package_info(
    'libPGSalientMatters',
    'https://www.sinsoul.com/data/libPGSalientMatters.zip',
    '04e429d64e40f76a7c5e35ad7d25533deea74b50744547423f89b1cff0585da7')
g_pkgs['pnnbase'] = package_info(
    'libPNNBaseDev',
    'https://www.sinsoul.com/data/libPNNBaseDev.zip',
    '4685a94ce95e8cb63161fa1e85fd225fd31b332658460d3de85d523ddf7c594e')
g_pkgs['g2o'] = package_info(
    'g2o',
    'https://www.sinsoul.com/data/g2o.zip',
    '8ed35097bbd300ad9034f1853d6fbef383d4b94aff32233a9b7fa36f8df0096f')
g_pkgs['dbow2'] = package_info(
    'dbow2',
    'https://www.sinsoul.com/data/DBoW2.zip',
    '5eb675a2188e67625b4a1580cda16ed25dd7814cc0bdf7bc90d71e5a2d7cc385')
g_pkgs['sophus'] = package_info(
    'sophus',
    'https://www.sinsoul.com/data/sophus.zip ',
    '00e1bbe51d926ae8344ce6345bdbf442c722aa7beaeb107dd50e47e9175b4209')
g_pkgs['ceres'] = package_info(
    'ceres',
    'https://www.sinsoul.com/data/ceres.zip ',
    '0c0aad68a666e4e3c4e153bcad95925d174d073c11a7b0d59d2ab9ccc43fb9cf')


class DependsSetup:
    def __init__(self):
        def print_output(line):
            print(line.rstrip())

        self.std_out = print_output
        self.self_path = os.path.abspath(__file__)
        self.current_dir = os.path.dirname(self.self_path)
        self.parent_dir = os.path.dirname(self.current_dir)
        self.depends_dir = os.path.join(self.parent_dir, 'depends')
        self.install_dir_lib = os.path.join(self.depends_dir, 'libs')
        self.install_dir_python = os.path.join(self.depends_dir, 'python')
        self.third_party_dir = os.path.join(self.parent_dir, 'third_party')
        self.temp_work_dir = tempfile.mkdtemp()
        self.touch = sh.Command('touch')
        self.cmake = sh.Command('cmake')
        self.python = sh.Command(os.path.join(
            distutils.sysconfig.get_config_var('BINDIR'), 'python'))
        self.pip = self.python.bake('-m', 'pip')
        self.protoc = None
        self.flatc = None
        self.func_registry()

    def clean_up(self):
        self.remove_files(self.temp_work_dir)

    @staticmethod
    def remove_files(dir_path):
        if os.path.isfile(dir_path):
            os.remove(dir_path)
        elif os.path.isdir(dir_path):
            shutil.rmtree(dir_path)
        else:
            return
        print('Remove: {}'.format(dir_path))

    def remove_files_with_glob(self, glob_path):
        for file in glob.glob(glob_path):
            self.remove_files(file)

    def remake_dirs(self, dir_path):
        self.remove_files(dir_path)
        os.makedirs(dir_path)

    def target_uninstall(self, target_list):
        for target in target_list:
            self.remove_files(target)

    def download_by_cmake(self, project_name, url, sha256sum, save_dir):
        cmake_name = '{}-downloader'.format(project_name)
        cmake_lists = 'cmake_minimum_required(VERSION 3.6 FATAL_ERROR)\n' \
                      'project(pgdnn-depends-setup)\n' \
                      'include(ExternalProject)\n' \
                      'externalproject_add({0:}\n' \
                      '    URL {1:}\n' \
                      '    URL_HASH SHA256={2:}\n' \
                      '    SOURCE_DIR "{3:}"\n' \
                      '    BINARY_DIR ""\n' \
                      '    CONFIGURE_COMMAND ""\n' \
                      '    BUILD_COMMAND ""\n' \
                      '    INSTALL_COMMAND ""\n' \
                      '    TEST_COMMAND ""\n' \
                      ')\n'.format(cmake_name, url, sha256sum, save_dir)

        cmake_dir = os.path.join(self.temp_work_dir, cmake_name)
        self.remake_dirs(cmake_dir)
        cmake_path = os.path.join(cmake_dir, 'CMakeLists.txt')
        with open(cmake_path, 'wb') as f:
            f.write(cmake_lists.encode())
            f.flush()
            f.close()

        self.cmake('.', _cwd=cmake_dir,
                   _out=self.std_out, _err_to_out=True, _tty_in=True)
        self.cmake('--build', '.', _cwd=cmake_dir,
                   _out=self.std_out, _err_to_out=True, _tty_in=True)

    def compile_by_cmake(self, generate_args, build_dir):
        self.remake_dirs(build_dir)
        self.cmake(*generate_args, _cwd=build_dir,
                   _out=self.std_out, _err_to_out=True, _tty_in=True)

        cmake_build_args = ('--build', '.', '--target', 'install', '--', '-j8')
        self.cmake(*cmake_build_args, _cwd=build_dir,
                   _out=self.std_out, _err_to_out=True, _tty_in=True)

    def protocol_buffers(self):
        pkg = g_pkgs['proto']
        build_dir = os.path.join(self.temp_work_dir, pkg.name)
        source_dir = os.path.join(self.depends_dir, pkg.name)
        install_dir_lib = os.path.join(self.install_dir_lib, pkg.name)
        self.download_by_cmake(pkg.name, pkg.url, pkg.hash, source_dir)

        cmake_generate_args = (os.path.join(source_dir, 'cmake'),
                               '-DCMAKE_BUILD_TYPE=Release',
                               '-DCMAKE_INSTALL_PREFIX={}'.format(install_dir_lib),
                               '-Dprotobuf_BUILD_TESTS=OFF')
        self.compile_by_cmake(cmake_generate_args, build_dir)

        protoc_path = os.path.join(install_dir_lib, 'bin', 'protoc')
        self.protoc = sh.Command(protoc_path)
        os.environ['PROTOC'] = protoc_path
        self.python('setup.py', 'bdist_wheel',
                    _cwd=os.path.join(source_dir, 'python'),
                    _out=self.std_out, _err_to_out=True, _tty_in=True)

        install_wheel_args = ('install',
                              '--target={}'.format(self.install_dir_python),
                              '--no-deps',
                              '--no-index',
                              '--upgrade',
                              '--find-links=dist',
                              'protobuf')
        self.pip(*install_wheel_args,
                 _cwd=os.path.join(source_dir, 'python'),
                 _out=self.std_out, _err_to_out=True, _tty_in=True)

        caffe_src_dir = os.path.join(self.third_party_dir, 'caffe')
        caffe_out_dir = os.path.join(self.install_dir_python, 'caffe')
        self.remake_dirs(caffe_out_dir)
        gen_caffe_pb_args = ('-I={}'.format(caffe_src_dir),
                             '--python_out={}'.format(caffe_out_dir),
                             os.path.join(caffe_src_dir, 'caffe.proto'))
        self.protoc(*gen_caffe_pb_args,
                    _out=self.std_out, _err_to_out=True, _tty_in=True)

        self.touch(os.path.join(caffe_out_dir, '__init__.py'))

    def protocol_buffers_uninstall(self):
        pkg = g_pkgs['proto']
        target_list = list()
        target_list.append(os.path.join(self.depends_dir, pkg.name))
        target_list.append(os.path.join(self.install_dir_lib, pkg.name))
        target_list.append(os.path.join(self.install_dir_python, 'google'))
        target_list.append(os.path.join(self.install_dir_python, 'caffe'))
        self.target_uninstall(target_list)
        self.remove_files_with_glob('{}/protobuf-*'.format(self.install_dir_python))

    def flat_buffers(self):
        pkg = g_pkgs['flat']
        build_dir = os.path.join(self.temp_work_dir, pkg.name)
        source_dir = os.path.join(self.depends_dir, pkg.name)
        install_dir_lib = os.path.join(self.install_dir_lib, pkg.name)
        self.download_by_cmake(pkg.name, pkg.url, pkg.hash, source_dir)

        cmake_generate_args = (source_dir,
                               '-DCMAKE_BUILD_TYPE=Release',
                               '-DFLATBUFFERS_BUILD_TESTS=OFF',
                               '-DCMAKE_INSTALL_PREFIX={}'.format(install_dir_lib))
        self.compile_by_cmake(cmake_generate_args, build_dir)

        flatc_path = os.path.join(install_dir_lib, 'bin', 'flatc')
        self.flatc = sh.Command(flatc_path)

        self.python('setup.py', 'bdist_wheel',
                    _cwd=os.path.join(source_dir, 'python'),
                    _out=self.std_out, _err_to_out=True, _tty_in=True)

        install_wheel_args = ('install',
                              '--target={}'.format(self.install_dir_python),
                              '--no-deps',
                              '--no-index',
                              '--upgrade',
                              '--find-links=dist',
                              'flatbuffers')
        self.pip(*install_wheel_args,
                 _cwd=os.path.join(source_dir, 'python'),
                 _out=self.std_out, _err_to_out=True, _tty_in=True)

        fbs_path = os.path.join(self.parent_dir, 'pgdnn', 'schema', 'pgdnn.fbs')
        gen_cpp_args = ('--cpp', '--gen-object-api',
                        '-o', os.path.join(self.depends_dir, 'pgdnn'), fbs_path)
        self.flatc(*gen_cpp_args, _cwd=self.parent_dir,
                   _out=self.std_out, _err_to_out=True, _tty_in=True)

        gen_py_args = ('--python', '--gen-object-api',
                       '-o', self.install_dir_python, fbs_path)
        self.flatc(*gen_py_args, _cwd=self.parent_dir,
                   _out=self.std_out, _err_to_out=True, _tty_in=True)

    def flat_buffers_uninstall(self):
        pkg = g_pkgs['flat']
        target_list = list()
        target_list.append(os.path.join(self.depends_dir, pkg.name))
        target_list.append(os.path.join(self.depends_dir, 'pgdnn'))
        target_list.append(os.path.join(self.install_dir_lib, pkg.name))
        target_list.append(os.path.join(self.install_dir_python, pkg.name))
        target_list.append(os.path.join(self.install_dir_python, 'pgd'))
        self.target_uninstall(target_list)
        self.remove_files_with_glob('{}/flatbuffers-*'.format(self.install_dir_python))

    def common_install(self, pkg_key):
        pkg = g_pkgs[pkg_key]
        source_dir = os.path.join(self.depends_dir, pkg.name)
        self.download_by_cmake(pkg.name, pkg.url, pkg.hash, source_dir)

    def common_uninstall(self, pkg_key):
        pkg = g_pkgs[pkg_key]
        target_list = list()
        target_list.append(os.path.join(self.depends_dir, pkg.name))
        self.target_uninstall(target_list)

    def func_registry(self):
        def func_register(name, install, uninstall):
            g_pkgs[name]['install'] = install
            g_pkgs[name]['uninstall'] = uninstall
        def common_register(name):
            g_pkgs[name]['install'] = lambda : self.common_install(name)
            g_pkgs[name]['uninstall'] = lambda : self.common_uninstall(name)
        for k, v in g_pkgs.items():
            common_register(k)
        func_register('proto', self.protocol_buffers, self.protocol_buffers_uninstall)
        func_register('flat', self.flat_buffers, self.flat_buffers_uninstall)

    def setup(self, action, packages):
        for package in packages:
            if package in list(g_pkgs.keys()):
                g_pkgs[package].install() if action == 'install' \
                    else g_pkgs[package].uninstall()
            else:
                print('Error: Unknown package name: {}'.format(package), file=sys.stderr)
                sys.exit(-1)


def update_self():
    update_url = 'https://www.sinsoul.com/data/depends_setup.py'
    print('Downloading ... ')
    result_file = requests.get(update_url)
    output_path = sys.argv[0]
    with open(output_path, 'wb') as f:
        f.write(result_file.content)
    print('Done')
    exit(0)


def parse_args():
    parser = argparse.ArgumentParser(description='PGDNN Depends Setup Tool')
    parser.add_argument('action',
                        help='install | uninstall | update',
                        type=str)
    args = parser.parse_known_args()
    if args[0].action == 'update':
        update_self()
    parser.add_argument('package',
                        help='all | ' + ' | '.join(list(g_pkgs.keys())),
                        type=str)
    args = parser.parse_args()
    return args


def main():
    args = parse_args()
    depends_setup = DependsSetup()
    packages = list()
    if args.package.lower() == 'all':
        packages = list(g_pkgs.keys())
    else:
        packages.append(args.package.lower())

    if args.action.lower() == 'install':
        try:
            depends_setup.setup(args.action.lower(), packages)
            depends_setup.clean_up()
        finally:
            depends_setup.clean_up()
    elif args.action.lower() == 'uninstall':
        depends_setup.setup(args.action.lower(), packages)
    else:
        print('Error: Invalid action type.', file=sys.stderr)
        sys.exit(-1)


if __name__ == '__main__':
    main()
