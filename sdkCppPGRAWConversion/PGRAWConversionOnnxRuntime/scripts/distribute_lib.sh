#!/bin/bash

set -e

lib_name="libPGRAWConversion.a"
dist_dir="libPGRAWConversion"

install_dir="install"

platforms=(
    "android-arm64-v8a"
    "android-armeabi-v7a"
    "android-x86_64"
    "Darwin-x86_64"
    "Darwin-arm64"
    "ios-arm64"
    "ios-armv7s"
    "ios-armv7"
    "ios-arm64e"
    "ios-x86_64"
    "Linux-x86_64"
    "Windows-x86_64"
)

sub_dirs=(
    "bin"
    "lib"
    "test"
    "include"
)

rm -rf ${dist_dir}
mkdir ${dist_dir}

latest_plat=""
for plat in "${platforms[@]}"; do
    if [ ! -d "${install_dir}/${plat}" ]; then
        echo "${install_dir}/${plat} not exists."
        continue
    fi
    latest_plat=${plat}
    mkdir -p ${dist_dir}/libs
    cp -r ${install_dir}/${plat} ${dist_dir}/libs
    mv ${dist_dir}/libs/${plat}/lib/${lib_name} ${dist_dir}/libs/${plat}/${lib_name}

    for sub_dir in "${sub_dirs[@]}"; do
        if [ -d "${dist_dir}/libs/${plat}/${sub_dir}" ]; then
            echo "Remove: ${dist_dir}/libs/${plat}/${sub_dir}"
            rm -rf ${dist_dir}/libs/${plat}/${sub_dir}
        fi
    done
done

cp -r ${install_dir}/${latest_plat}/include ${dist_dir}

if [ -z "${ANDROID_NDK}" ]; then
    echo "ANDROID_NDK not set. Skip strip steps"
    exit 0
fi

if [ -f "${dist_dir}/libs/android-arm64-v8a/${lib_name}" ]; then
    echo "strip android-arm64-v8a"
    $ANDROID_NDK/toolchains/aarch64-linux-android-4.9/prebuilt/darwin-x86_64/bin/aarch64-linux-android-strip -d "${dist_dir}/libs/android-arm64-v8a/${lib_name}"
fi

if [ -f "${dist_dir}/libs/android-armeabi-v7a/${lib_name}" ]; then
    echo "strip android-armeabi-v7a"
    $ANDROID_NDK/toolchains/arm-linux-androideabi-4.9/prebuilt/darwin-x86_64/bin/arm-linux-androideabi-strip -d "${dist_dir}/libs/android-armeabi-v7a/${lib_name}"
fi

if [ -f "${dist_dir}/libs/android-x86_64/${lib_name}" ]; then
    echo "strip android-x86_64"
    $ANDROID_NDK/toolchains/x86_64-4.9/prebuilt/darwin-x86_64/bin/x86_64-linux-android-strip -d "${dist_dir}/libs/android-x86_64/${lib_name}"
fi

echo "Clear macOS system files"
rm -rf $(find ${dist_dir} -name ".DS_*")

# copy files
echo "Copy sdk files"
cp -r "../ChangeLog.md" ${dist_dir}
cp -r "../README.md" ${dist_dir}

# test dir
if [ ! -d "${dist_dir}/test" ]; then
    mkdir ${dist_dir}/test
    # 假设测试文件在项目根目录的 test 文件夹下
    if [ -d "../test" ]; then
        cp -r "../test/"* "${dist_dir}/test/"
    else
        echo "Warning: ../test directory not found"
    fi
fi
