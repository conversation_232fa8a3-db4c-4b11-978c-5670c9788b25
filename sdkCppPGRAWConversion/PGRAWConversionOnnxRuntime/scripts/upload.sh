#!/usr/bin/env bash

function to_android() {
  local debug_port=${1}
  local debug_app=${2}
  local params="${@:3}"
  local file_name=$(basename -- "${debug_app}")
  if [ ${debug_port} == "0" ]
  then
      debug_cmd=""
  else
      debug_cmd="./gdbserver :${debug_port}"
      #debug_cmd="./lldb-server platform --server --listen 127.0.0.1:${debug_port}"
      #debug_cmd="./lldb-server gdbserver 127.0.0.1:${debug_port}"
      adb forward tcp:${debug_port} tcp:${debug_port}
  fi

  echo "uploading..."
  adb push ${debug_app} /data/local/tmp/${file_name}
  echo "running...: ${debug_cmd} ./${file_name} ${params}"
  echo "url: tcp:127.0.0.1:${debug_port}"
  adb shell "cd /data/local/tmp && ${debug_cmd} ./${file_name} ${params}"
}

function create_sign_xml() {
cat > temporary_ent.xml << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>com.apple.springboard.debugapplications</key>
        <true/>
        <key>get-task-allow</key>
        <true/>
        <key>task_for_pid-allow</key>
        <true/>
        <key>run-unsigned-code</key>
        <true/>
    </dict>
</plist>
EOF
}

function to_ios() {
  local ssh_host=127.0.0.1
  local ssh_remote_port=22
  local ssh_local_port=2222
  local ssh_passwd=initiald
  local debug_port=${1}
  local debug_app=${2}
  local params="${@:3}"
  local file_name=$(basename -- "${debug_app}")
  if [ ${debug_port} == "0" ]
  then
      debug_cmd=""
      param_sep=""
  else
      debug_cmd="/usr/local/bin/debugserver_arm64 0.0.0.0:${debug_port}"
      param_sep="--"
  fi

  echo "port forwarding..."
  (iproxy ${ssh_local_port} ${ssh_remote_port} &) &> /dev/null
  (iproxy ${debug_port} ${debug_port} &) &> /dev/null

  echo "resigning..."
  create_sign_xml
  cp ${debug_app} ${debug_app}.sign
  jtool --sign --inplace --ent temporary_ent.xml ${debug_app}.sign

  echo "uploading..."
  sshpass -p "${ssh_passwd}" ssh -p ${ssh_local_port} -oLogLevel=ERROR -oStrictHostKeyChecking=no -oUserKnownHostsFile=/dev/null root@${ssh_host} "rm -rf /usr/local/bin/${file_name}"
  sshpass -p "${ssh_passwd}" scp -P ${ssh_local_port} -oLogLevel=ERROR -oStrictHostKeyChecking=no -oUserKnownHostsFile=/dev/null ${debug_app}.sign root@${ssh_host}:/usr/local/bin/${file_name}
  # sshpass -p "${ssh_passwd}" rsync -r -v --progress -e "ssh -p ${ssh_local_port} -oLogLevel=ERROR -oStrictHostKeyChecking=no -oUserKnownHostsFile=/dev/null" ${debug_app}.sign root@${ssh_host}:/usr/local/bin/${file_name}
  rm -rf ${2}.sign
  rm -rf temporary_ent.xml

  echo "running...: ${file_name} ${params}"
  echo "url: connect://${ssh_host}:${debug_port}"
  sshpass -p "${ssh_passwd}" ssh -p ${ssh_local_port} -oLogLevel=ERROR -oStrictHostKeyChecking=no -oUserKnownHostsFile=/dev/null root@${ssh_host} "${debug_cmd} /usr/local/bin/${file_name} ${param_sep} ${params}"

  echo "cleaning..."
  killall iproxy
}

set -e

TARGET_OS=$(echo ${1} | awk '{print tolower($0)}')

if [ "${TARGET_OS}" == "android" ]; then
    to_android "${@:2}"
elif [ "${TARGET_OS}" == "ios" ]; then
    to_ios "${@:2}"
else
    echo "Remote Debug Assistant"
    echo "Usage: ${0} <Target OS> <Debug Port> <App>"
    echo "Target OS: android | ios"
fi
