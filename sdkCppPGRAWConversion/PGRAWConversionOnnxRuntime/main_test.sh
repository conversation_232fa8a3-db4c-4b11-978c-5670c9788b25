#!/usr/bin/env bash
set -e

# 路径
BIN_PATH='./build/raw_conversion/app/raw_conversion'                # './build-output/install/Darwin-x86_64/bin/raw_conversion'
IMAGE_INP_DIR='Data/1006研发过程/图像转档测试集结果/原始的PNG16'                    # 输入目录
IMAGE_OUT_DIR='Data/1006研发过程/图像转档测试集结果/原始的PNG16_ORT_转档结果_不做去噪_Auto' # 输出目录

# 日期
DATE_SUFFIX=$(date +"%Y-%m-%d")
IMAGE_OUT_DIR=${IMAGE_OUT_DIR}-${DATE_SUFFIX}

# 执行
rm -rf ${IMAGE_OUT_DIR} && mkdir ${IMAGE_OUT_DIR}
for pic_file in $(ls ${IMAGE_INP_DIR}); do
    echo -e "\nProcessing ${pic_file} ..."
    ${BIN_PATH} --input=${IMAGE_INP_DIR}/${pic_file} --output=${IMAGE_OUT_DIR}/${pic_file//'.PNG'/'.jpg'}
    # cp -f ${IMAGE_INP_DIR}/${pic_file} ${IMAGE_OUT_DIR}/${pic_file}
done
