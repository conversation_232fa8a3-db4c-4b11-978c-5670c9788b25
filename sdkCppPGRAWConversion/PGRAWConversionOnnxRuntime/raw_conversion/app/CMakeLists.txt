# 设置stb_image库的源代码目录路径
set(STBIMG_SRC_DIR "${PGRAWCVT_DEPENDENCIES_SOURCE_DIR}/stb_image")
# 检查stb_image库的源代码目录是否存在
dependency_check(${STBIMG_SRC_DIR} "stbimg" "stb_image")

# 设置args库的源代码目录路径
set(ARGS_SRC_DIR "${PGRAWCVT_DEPENDENCIES_SOURCE_DIR}/args")
# 检查args库的源代码目录是否存在
dependency_check(${ARGS_SRC_DIR} "args" "args.hxx")

# 添加一个可执行文件目标，名为raw_conversion，源文件是main.cpp
add_executable(raw_conversion main.cpp)
# 设置raw_conversion目标的C++标准为C++11
set_property(TARGET raw_conversion PROPERTY CXX_STANDARD 11)

# 根据不同平台设置编译标志
if(WIN32)
    # 如果是Windows平台，设置编译标志以启用C++异常处理
    set_property(TARGET raw_conversion PROPERTY COMPILE_FLAGS "-Xclang -fcxx-exceptions")
else()
    # 对于其他平台，设置编译标志以启用异常处理
    set_property(TARGET raw_conversion PROPERTY COMPILE_FLAGS -fexceptions)
endif()

# 设置raw_conversion目标的编译标志以启用异常处理
set_property(TARGET raw_conversion PROPERTY COMPILE_FLAGS -fexceptions)
# 设置raw_conversion目标的链接标志
set_property(TARGET raw_conversion PROPERTY LINK_FLAGS ${PGRAWCVT_LINK_FLAGS})
# 设置raw_conversion目标的包含目录，包括项目源目录和所有依赖项的源代码目录
set_property(
    TARGET raw_conversion 
    PROPERTY INCLUDE_DIRECTORIES 
    ${CMAKE_SOURCE_DIR} 
    ${STBIMG_SRC_DIR} 
    ${ARGS_SRC_DIR} 
    ${LIB_AUTH_INC} 
    ${ORT_INC_DIR} 
    ${GLFW3_INC_DIR} 
    ${GLEW_INC_DIR} 
    ${OPENGL_INCLUDE_DIRS})
# 设置raw_conversion目标的链接库，包括静态库、操作系统特定的库、OpenCV库、ONNXRuntime库和权限库
target_link_libraries(raw_conversion PGRAWConversionStatic ${PGRAWCVT_OS_LIBS} ${OpenCV_LIBS} ${ORT_LIBS} ${LIB_AUTH_LIB})

# 安装raw_conversion可执行文件到系统的二进制目录
install(TARGETS raw_conversion RUNTIME DESTINATION bin)
