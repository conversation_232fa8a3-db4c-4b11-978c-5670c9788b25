//
// PGRAWConversion
//
// Created by Tanqy on 2024/05/30.
// Copyright © 2024 Tanqy. All rights reserved.
//

// 定义STB图像处理库的实现部分
#define STB_IMAGE_IMPLEMENTATION
// 定义STB图像写入库的实现部分
#define STB_IMAGE_WRITE_IMPLEMENTATION

// 标准库引入
#include <fstream>  // 引入文件流库
#include <iostream> // 引入标准输入输出库

// 第三方库引入
#include "args.hxx"          // 引入args库，用于处理命令行参数
#include "stb_image.h"       // 引入STB图像读取库
#include "stb_image_write.h" // 引入STB图像写入库

// 项目内部头文件引入
#include "raw_conversion/raw_conversion/raw_conversion.hpp" // 引入RAWConversion类的声明

/**
 * 授权信息结构体
 *
 * 用于存储用户授权相关的信息，包括用户代码、平台和产品信息以及授权密钥
 */
struct AuthorizationInfo {
    std::string user_code;                                                   // 用户代码
    std::vector<std::pair<std::string, std::string>> platforms_and_products; // 平台和产品信息列表
    std::string authorized_key;                                              // 授权密钥
};

/**
 * 从指定文件路径中读取并解析授权信息
 *
 * @param file_path 待解析的文件路径
 * @param info 解析后得到的授权信息
 * @return 如果解析成功，则返回 true；否则返回 false
 */
bool parse_authorization_info(std::string file_path, AuthorizationInfo &info) {
    // 打开授权文件
    std::ifstream input(file_path);
    if (!input.is_open()) {
        std::cerr << "Error: failed to open file " << file_path << std::endl;
        return false;
    }

    // 逐行读取并解析文件内容
    std::string line;
    while (std::getline(input, line)) {
        // 解析用户代码
        if (line.find("USER CODE:") != std::string::npos) {
            std::getline(input, line);
            info.user_code = line.substr();
        }
        // 解析平台和产品信息
        else if (!line.empty() && line.find("PLATFORM:") != std::string::npos) {
            std::string platform = line.substr(10);
            std::getline(input, line);
            if (line.find("PRODUCTS:") == std::string::npos) {
                std::cerr << "Error: invalid format" << std::endl;
                return false;
            }
            std::getline(input, line);
            std::string products = line.substr();
            info.platforms_and_products.emplace_back(platform, products);
        }
        // 解析授权密钥
        else if (!line.empty() && line.find("AUTHORIZED KEY:") != std::string::npos) {
            std::getline(input, line);
            info.authorized_key = line.substr();
        }
    }

    return true;
}

namespace pgrawcvt { // 定义命名空间pgrawcvt

/**
 * 主函数 - 实现RAW图像转换的核心功能
 *
 * @param argc 命令行参数数量
 * @param argv 命令行参数数组
 * @return 程序执行状态码，0表示成功，非0表示失败
 */
int main(int argc, char **argv) {
    // 设置环境变量，控制日志输出级别
#if defined _WIN32 || defined __CYGWIN__
    _putenv_s("PGRAWCVT_CPP_MIN_VLOG_LEVEL", "1"); // 开启日志信息输出
    // _putenv_s("PGRAWCVT_CPP_DEBUG_DIR", "./");     // 开启中间图像输出
#else
    setenv("PGRAWCVT_CPP_MIN_VLOG_LEVEL", "1", 1); // 开启日志信息输出
    setenv("PGRAWCVT_CPP_DEBUG_DIR", "./", 1);     // 开启中间图像输出
#endif

    // 读取并验证授权信息
    AuthorizationInfo info;
    if (!parse_authorization_info("<EMAIL>", info)) {
        return 1;
    }
    std::string key = info.authorized_key;
    std::string user_code = info.user_code;
    std::string prod_code = info.platforms_and_products.front().second; // 所有平台使用相同的产品代码

    // 创建命令行参数解析器
    args::ArgumentParser parser("PinGuo RAWCVT", "");
    args::HelpFlag help(parser, "help", "Display this help menu", {'h', "help"});
    args::ValueFlag<std::string> input_path(parser, "image path", "Image file path", {"input"}, "");
    args::ValueFlag<std::string> output_path(parser, "output path", "Output file path", {"output"}, "");

    // 解析命令行参数
    try {
        parser.ParseCLI(argc, static_cast<const char *const *>(argv));
    } catch (const args::Help &) {
        std::cout << parser;
        return 0;
    } catch (const args::ParseError &e) {
        std::cerr << e.what() << std::endl;
        std::cerr << parser;
        return -1;
    } catch (const args::ValidationError &e) {
        std::cerr << e.what() << std::endl;
        std::cerr << parser;
        return -1;
    }

    // 配置ONNX运行环境
    std::unique_ptr<OrtConfig> ort_conf_ptr(new OrtConfig());                                        // 创建OrtConfig对象的智能指针
    ort_conf_ptr->env = std::unique_ptr<Ort::Env>(new Ort::Env(ORT_LOGGING_LEVEL_ERROR, "LOG_TAG")); // 设置ONNX环境

    // 创建CPU内存信息
    OrtMemoryInfo *p;
    Ort::ThrowOnError(Ort::GetApi().CreateCpuMemoryInfo(OrtArenaAllocator, OrtMemTypeDefault, &p));
    ort_conf_ptr->cpu_arena_mem = std::make_shared<Ort::MemoryInfo>(p);

    // 配置ONNX会话选项
    ort_conf_ptr->session_opts.SetGraphOptimizationLevel(GraphOptimizationLevel::ORT_ENABLE_ALL); // 启用所有图优化
    ort_conf_ptr->session_opts.SetLogSeverityLevel(ORT_LOGGING_LEVEL_ERROR);                      // 设置日志级别
    ort_conf_ptr->session_opts.SetIntraOpNumThreads(4);                                           // 设置线程数

#define USE_CUDA 1

    // 配置ONNX执行设备
    std::string ep_name = "cpu"; // 默认使用CPU
    std::string device_id = "0";

    if (ep_name == "cpu") {
        // CPU设备配置（如需特殊配置可在此处添加）
    } else if (ep_name == "cuda") {
        // GPU设备配置
#if USE_CUDA == 1
        // 创建并配置CUDA提供程序选项
        OrtCUDAProviderOptionsV2 *options;
        Ort::ThrowOnError(Ort::GetApi().CreateCUDAProviderOptions(&options));
        std::vector<const char *> keys{"device_id"};
        std::vector<const char *> values{"0"};
        Ort::ThrowOnError(Ort::GetApi().UpdateCUDAProviderOptions(options, keys.data(), values.data(), keys.size()));
        ort_conf_ptr->session_opts.AppendExecutionProvider_CUDA_V2(*options);
        Ort::GetApi().ReleaseCUDAProviderOptions(options);
#endif
    }

    // 读取输入图像
    cv::Mat srcImage = cv::imread(args::get(input_path).c_str(), cv::IMREAD_UNCHANGED);

    for (int i = 0; i < 1; i++) {

        // ************************************** RAWConversion初始化 ****************************************
        // TODO: 创建图像转档类实例
        RAWConversion raw_conversion;

        // 记录初始化开始时间
        auto fl_init_tic = std::chrono::system_clock::now();

        // TODO: 初始化RAWConversion
        bool enable_opengl = true; // 启用OpenGL加速4DLUT计算过程
        bool status_init = raw_conversion.init(ort_conf_ptr.get(), key, user_code, prod_code, enable_opengl);

        // TODO: 配置RAWConversion参数
        RAWCVTConfig rawcvt_config(false, false, true, RAWCVTConfig::NORMAL, -1);

        // 检查初始化状态
        if (status_init == false) {
            fprintf(stderr, "Failed to init RAWConversion.\n");
            return -1;
        }

        // 计算并输出初始化耗时
        auto fl_init_toc = std::chrono::system_clock::now();
        auto fl_init_use = std::chrono::duration_cast<std::chrono::milliseconds>(fl_init_toc - fl_init_tic);
        std::cout << "Time of init: " << fl_init_use.count() << " ms; " << std::endl;
        // -----------------------------------------------------------------------------------------------------

        // ************************************** RAWConversion执行 ****************************************
        cv::Mat dstImage;
        // 记录执行开始时间
        auto fl_exec_tic = std::chrono::system_clock::now();

        // TODO: 执行RAW转换
        int adjust_mode;                                                                                         // 获取分类器结果
        bool adjust_custom = true;                                                                               // 是否启用支持客户已有JPG调标准色
        bool status_exec = raw_conversion.run(rawcvt_config, srcImage, dstImage, 2, adjust_mode, adjust_custom); // 执行转换

        // 检查执行状态
        if (status_exec == false) {
            fprintf(stderr, "Failed to exec RAWConversion.\n");
            return -1;
        }

        // 计算并输出执行耗时
        auto fl_exec_toc = std::chrono::system_clock::now();
        auto fl_exec_use = std::chrono::duration_cast<std::chrono::milliseconds>(fl_exec_toc - fl_exec_tic);
        std::cout << "Time of exec: " << fl_exec_use.count() << " ms, adjust_mode is " << adjust_mode << "." << std::endl;
        // -----------------------------------------------------------------------------------------------------

        // 处理输出图像并保存
        if (dstImage.depth() == CV_16U) {
            cv::normalize(dstImage, dstImage, 0, 255, cv::NORM_MINMAX, CV_8U); // 如果是16位图像，就归一化到8位
        }
        // 保存结果图像（JPEG格式，最高质量）
        cv::imwrite(args::get(output_path).c_str(), dstImage, {cv::IMWRITE_JPEG_QUALITY, 100});
        raw_conversion.clear();
    }
    return 0;
}

} // namespace pgrawcvt

int main(int argc, char **argv) { return pgrawcvt::main(argc, argv); }
