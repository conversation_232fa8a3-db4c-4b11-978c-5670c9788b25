# 输出空行以增加可读性
message("\n")

# 依赖项 - 权限库
# 设置权限库的头文件和库文件路径
set(LIB_AUTH_INC ${PGRAWCVT_DEPENDENCIES_SOURCE_DIR}/libPGAuthoritySystem/include)
set(LIB_AUTH_LIB ${PGRAWCVT_DEPENDENCIES_SOURCE_DIR}/libPGAuthoritySystem/libs/${PGRAWCVT_OS}-${PGRAWCVT_ARCH}/libPGAuthoritySystem.${LIB_SUFFIX})
# 输出权限库的路径状态信息
message(STATUS "LIB_AUTH_LIB = ${LIB_AUTH_LIB}")
# 检查权限库是否存在
dependency_check(${LIB_AUTH_LIB} "authority" "libPGAuthoritySystem")

# 依赖项 - OpenCV
# 设置OpenCV库的路径
set(OPENCV_LIB_DIR "${PGRAWCVT_DEPENDENCIES_SOURCE_DIR}/opencv")
# 检查OpenCV库是否存在
dependency_check(${OPENCV_LIB_DIR} "opencv" "OpenCV")
# 根据不同平台设置OpenCV的路径
if(ANDROID)
    set(OpenCV_DIR ${OPENCV_LIB_DIR}/${PGRAWCVT_OS}-${PGRAWCVT_ARCH}/sdk/native/jni)
elseif(WIN32)
    set(OpenCV_DIR ${OPENCV_LIB_DIR}/${PGRAWCVT_OS}-${PGRAWCVT_ARCH}/x64/vc17/staticlib)
    # 设置zlib和jpeg库的路径（这些是OpenCV的依赖项）
    set(LIB_ZLIB_PATH ${OPENCV_LIB_DIR}/${PGRAWCVT_OS}-${PGRAWCVT_ARCH}/3rd/zlibstatic.lib)
    set(LIB_JPEG_PATH ${OPENCV_LIB_DIR}/${PGRAWCVT_OS}-${PGRAWCVT_ARCH}/3rd/jpeg-static.lib)
    # 将zlib和jpeg库添加到操作系统库列表中
    list(APPEND PGRAWCVT_OS_LIBS ${LIB_ZLIB_PATH} ${LIB_JPEG_PATH})
    # 将OpenCV库添加到操作系统库列表中
    list(APPEND PGRAWCVT_OS_LIBS ${OpenCV_LIBS})
else()
    set(OpenCV_DIR ${OPENCV_LIB_DIR}/${PGRAWCVT_OS}-${PGRAWCVT_ARCH}/lib/cmake/opencv4)
endif()
# 输出OpenCV的路径状态信息
message(STATUS "OpenCV_DIR = ${OpenCV_DIR}")
# 查找并加载OpenCV配置
find_package(OpenCV REQUIRED)

# Opengl 3.0
message(STATUS "USE_OPENGL = ${USE_OPENGL}")
if(USE_OPENGL)
    # add PGGC
    add_subdirectory(general_computing)
endif()

message(STATUS "USE_OPENGL = ${USE_OPENGL}")
if(USE_OPENGL)
    if(APPLE)
        execute_process(COMMAND xcrun --show-sdk-path OUTPUT_VARIABLE SDK_PATH OUTPUT_STRIP_TRAILING_WHITESPACE)
        set(CMAKE_OSX_SYSROOT ${SDK_PATH})
        find_package(glfw3 REQUIRED)
        find_package(GLEW REQUIRED)
        find_package(OpenGL REQUIRED)
        list(APPEND PGRAWCVT_OS_LIBS glfw GLEW::GLEW)
    elseif(WIN32)
        set(GLFW3_DIR ${PGRAWCVT_DEPENDENCIES_SOURCE_DIR}/glfw/glfw-3.4.bin.WIN64)
        dependency_check(${GLFW3_DIR} "glfw" "glfw3")
        set(GLFW3_INC_DIR ${GLFW3_DIR}/include)
        set(GLFW3_LIB_DIR ${GLFW3_DIR}/lib-vc2017)
        set(GLFW3_LIB ${GLFW3_LIB_DIR}/glfw3_mt.lib)
        list(APPEND PGRAWCVT_OS_LIBS ${GLFW3_LIB})
        list(APPEND PGRAWCVT_INC_DEPENDS ${GLFW3_INC_DIR})
        list(APPEND PGRAWCVT_APP_SO_INSTALL_DIRS ${GLFW3_LIB_DIR})

        set(GLEW_DIR ${PGRAWCVT_DEPENDENCIES_SOURCE_DIR}/glew)
        dependency_check(${GLEW_DIR} "glew" "glew")
        set(GLEW_INC_DIR ${GLEW_DIR}/${PGRAWCVT_OS}/include)
        set(GLEW_LIB_DIR ${GLEW_DIR}/${PGRAWCVT_OS}/lib/Release/x64)
        set(GLEW_LIB ${GLEW_LIB_DIR}/glew32.lib)
        list(APPEND PGRAWCVT_OS_LIBS ${GLEW_LIB})
        list(APPEND PGRAWCVT_INC_DEPENDS ${GLEW_INC_DIR})
        list(APPEND PGRAWCVT_APP_SO_INSTALL_DIRS ${GLEW_DIR}/${PGRAWCVT_OS}/bin/Release/x64)

        find_package(OpenGL REQUIRED)
        list(APPEND PGRAWCVT_OS_LIBS ${OPENGL_LIBRARIES})
        list(APPEND PGRAWCVT_INC_DEPENDS ${OPENGL_INCLUDE_DIRS})

        # 打印调试信息，查看包含目录是否正确设置
        message(STATUS "GLFW3_INC_DIR = ${GLFW3_INC_DIR}")
        message(STATUS "GLEW_INC_DIR = ${GLEW_INC_DIR}")
        message(STATUS "OPENGL_INCLUDE_DIRS = ${OPENGL_INCLUDE_DIRS}")
        message(STATUS "PGRAWCVT_INC_DEPENDS = ${PGRAWCVT_INC_DEPENDS}")

        # 直接将包含目录添加到全局包含目录
        include_directories(${GLFW3_INC_DIR} ${GLEW_INC_DIR} ${OPENGL_INCLUDE_DIRS})
    else()
        find_package(glfw3 REQUIRED)
        find_package(GLEW REQUIRED)
        find_package(OpenGL REQUIRED)
        list(APPEND PGRAWCVT_OS_LIBS glfw GLEW::GLEW GL)
    endif()
    add_definitions(-DUSE_OPENGL)
endif()

# 依赖项 - ONNXRuntime
# 根据不同平台设置ONNXRuntime的头文件和库文件路径
if(APPLE)
    set(ORT_INC_DIR ${PGRAWCVT_DEPENDENCIES_SOURCE_DIR}/onnxruntime/${PGRAWCVT_OS}/${PGRAWCVT_ARCH}/include)
    set(ORT_LIBS ${PGRAWCVT_DEPENDENCIES_SOURCE_DIR}/onnxruntime/${PGRAWCVT_OS}/${PGRAWCVT_ARCH}/lib/libonnxruntime.dylib)
elseif(WIN32)
    # include
    set(ORT_INC_DIR ${PGRAWCVT_DEPENDENCIES_SOURCE_DIR}/onnxruntime/${PGRAWCVT_OS}/${PGRAWCVT_ARCH}/include)
    # lib
    file(GLOB ORT_LIBS ${PGRAWCVT_DEPENDENCIES_SOURCE_DIR}/onnxruntime/${PGRAWCVT_OS}/${PGRAWCVT_ARCH}/lib/*.lib)
    foreach(ORT_LIB ${ORT_LIBS})
        set(ORT_LIBS ${ORT_LIBS} ${ORT_LIB})
    endforeach()

    # 拷贝所有dll文件到应用目录
    file(GLOB ORT_DLLS ${PGRAWCVT_DEPENDENCIES_SOURCE_DIR}/onnxruntime/${PGRAWCVT_OS}/${PGRAWCVT_ARCH}/lib/*.dll)
    file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/raw_conversion/app)
    foreach(ORT_DLL ${ORT_DLLS})
        file(COPY ${ORT_DLL} DESTINATION ${CMAKE_BINARY_DIR}/raw_conversion/app)
    endforeach()
else()
    set(ORT_INC_DIR ${PGRAWCVT_DEPENDENCIES_SOURCE_DIR}/onnxruntime/${PGRAWCVT_OS}/${PGRAWCVT_ARCH}/include)
    set(ORT_LIBS ${PGRAWCVT_DEPENDENCIES_SOURCE_DIR}/onnxruntime/${PGRAWCVT_OS}/${PGRAWCVT_ARCH}/lib/libonnxruntime.so)
endif()
# 输出ONNXRuntime的头文件和库文件路径状态信息
message(STATUS "ORT_INC_DIR = ${ORT_INC_DIR}")
message(STATUS "ORT_LIBS = ${ORT_LIBS}")

# 源文件
# 查找所有的源代码和头文件
file(GLOB RAWCVT_SRC ${CMAKE_CURRENT_SOURCE_DIR}/raw_conversion/*.cpp)
file(GLOB RAWCVT_HDR ${CMAKE_CURRENT_SOURCE_DIR}/raw_conversion/*.hpp)

# 添加一个静态库目标，包含上面找到的源代码和头文件
add_library(PGRAWConversionStatic STATIC ${RAWCVT_SRC} ${RAWCVT_HDR} ${PGGC_SRC})
# 设置静态库的输出名称为"PGRAWConversion"
set_property(TARGET PGRAWConversionStatic PROPERTY OUTPUT_NAME "PGRAWConversion")
# 确保目标在每次构建时都会被清理
set_property(TARGET PGRAWConversionStatic PROPERTY CLEAN_DIRECT_OUTPUT 1)
# 设置C标准为C99
set_property(TARGET PGRAWConversionStatic PROPERTY C_STANDARD 99)
# 设置C++标准为C++11
set_property(TARGET PGRAWConversionStatic PROPERTY CXX_STANDARD 11)
# 设置编译标志，包括OpenMP的标志和位置无关代码的标志
set_property(TARGET PGRAWConversionStatic PROPERTY COMPILE_FLAGS "${OPENMP_INCLUDE_FLAG} -Xpreprocessor -fopenmp -fPIC")
# 设置包含目录，包括项目源目录和所有依赖项的头文件目录
set_property(
    TARGET PGRAWConversionStatic
    PROPERTY INCLUDE_DIRECTORIES
             ${CMAKE_SOURCE_DIR}
             ${LIB_AUTH_INC}
             ${ORT_INC_DIR}
             ${OpenCV_INCLUDE_DIRS}
             ${GLFW3_INC_DIR}
             ${GLEW_INC_DIR}
             ${OPENGL_INCLUDE_DIRS}
             ${CMAKE_CURRENT_SOURCE_DIR})
# 设置链接标志
set_property(TARGET PGRAWConversionStatic PROPERTY LINK_FLAGS "${PGRAWCVT_LINK_FLAGS}")
# 设置链接库，包括OpenMP和操作系统特定的库
target_link_libraries(PGRAWConversionStatic ${OPENMP_LINK_FLAG} ${PGRAWCVT_OS_LIBS})

# 如果是Windows平台，设置SDK、库和头文件的目录，并复制相应的文件
if(WIN32)
    set(Dir_SDK ${CMAKE_HOME_DIRECTORY}/build-output/install)
    set(Dir_Lib ${Dir_SDK}/${PGRAWCVT_OS}-${PGRAWCVT_ARCH}/lib)
    set(Dir_Inc ${Dir_SDK}/${PGRAWCVT_OS}-${PGRAWCVT_ARCH}/include)
    # 复制头文件到指定目录
    file(COPY raw_conversion/raw_conversion.hpp DESTINATION ${Dir_Inc})
    # 如果构建共享库，则复制DLL和导出库文件到指定目录
    if(BUILD_SHARED_LIBS)
        file(COPY ${CMAKE_BINARY_DIR}/raw_conversion/libPGRAWConversion.dll DESTINATION ${Dir_Lib})
        file(COPY ${CMAKE_BINARY_DIR}/raw_conversion/libPGRAWConversion.dll.a DESTINATION ${Dir_Lib})
    else()
        # 如果构建静态库，可以在这里复制静态库文件
        # file(COPY ${CMAKE_BINARY_DIR}/raw_conversion/Release/libPGRAWConversion.a DESTINATION ${Dir_Lib})
    endif()
else()
    # 对于非Windows平台，安装静态库和头文件到系统的库和包含目录
    install(TARGETS PGRAWConversionStatic ARCHIVE DESTINATION lib)
    install(FILES raw_conversion/raw_conversion.hpp DESTINATION include)
endif()

# Copy authority.*.ini to App Dir
message(STATUS "One more thing ...\n")
message(STATUS "SRC_DIR = ${CMAKE_SOURCE_DIR}")
message(STATUS "APP_DIR = ${CMAKE_BINARY_DIR}/raw_conversion/app\n")
file(COPY ${CMAKE_SOURCE_DIR}/<EMAIL> DESTINATION ${CMAKE_BINARY_DIR}/raw_conversion/app)

# 构建应用程序
# 添加子目录，这里通常是应用程序的源代码
add_subdirectory(app)
