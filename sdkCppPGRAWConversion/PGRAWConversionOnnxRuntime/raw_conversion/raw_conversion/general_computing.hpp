//
//  PGGPUGeneralComputing
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/7/10.
//  Copyright © 2019 ZhangJingQi. All rights reserved.
//

#ifndef GENERAL_COMPUTING_GENERAL_COMPUTING_GENERAL_COMPUTING_HPP_
#define GENERAL_COMPUTING_GENERAL_COMPUTING_GENERAL_COMPUTING_HPP_

#include <cmath>
#include <memory>
#include <string>
#include <vector>

namespace pgrawcvt {

// 确保与logger.hpp中的定义一致
typedef void (*LogCallback)(const char *message);

/// 算法接口类
class GeneralComputing {
  public:
    GeneralComputing();
    ~GeneralComputing();

    /// \brief 初始化
    ///
    /// 初始化
    /// \return 成功返回 true，失败返回 false.
    bool init();

    /// \brief 执行算法
    bool run(const void *psrc_data, void *pdst_data, int width, int height, int elem_type, const float *luts, std::vector<int64_t> &luts_shape, const float *maps, std::vector<int64_t> &maps_shape);

    /// \brief 清理类
    ///
    /// 清理算法资源
    /// \return
    void clear();

    void set_log_callback(LogCallback log_func);

  public:
    class GeneralComputingInner;
    std::unique_ptr<GeneralComputingInner> inner_;
};

} // namespace pgrawcvt

#endif // GENERAL_COMPUTING_GENERAL_COMPUTING_GENERAL_COMPUTING_HPP_
