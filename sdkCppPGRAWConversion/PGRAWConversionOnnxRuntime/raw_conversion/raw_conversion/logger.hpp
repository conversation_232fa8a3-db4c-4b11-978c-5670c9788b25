//
//  PGMotionTracker
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/7/10.
//  Copyright © 2019 ZhangJingQi. All rights reserved.
//

#ifndef RAW_CONVERSION_RAW_CONVERSION_LOGGER_HPP_
#define RAW_CONVERSION_RAW_CONVERSION_LOGGER_HPP_

#include <cstring>
#include <iostream>
#include <memory>
#include <stack>
#include <string>
#include <vector>

namespace pgrawcvt {

// 定义LogCallback类型，与general_computing.hpp保持一致
typedef void (*LogCallback)(const char *message);

class Logger {
  public:
    static Logger *SingletonLogger();

    Logger();

    static std::int64_t now_micros();

    void latency_start();

    void log_info(const char *fmt, ...);

    void latency_log(const char *fmt, ...);

    std::string path_append(const std::string &p1, const std::string &p2);

    void write_ppm(const char *name, const std::uint8_t *buffer, std::uint32_t width, std::uint32_t height, std::uint32_t channel);

    void write_pgm(const char *name, const std::uint8_t *buffer, std::uint32_t width, std::uint32_t height, std::uint32_t channel);

    void save_image(const char *name, const std::uint8_t *buffer, std::uint32_t width, std::uint32_t height, std::uint32_t channel);

    void save_2flow(const char *name, const std::uint8_t *buffer, std::uint32_t width, std::uint32_t height);

    void save_tensor(const char *name, const float *buffer, std::uint32_t width, std::uint32_t height, std::uint32_t channel, const std::vector<float> &mean, const std::vector<float> &norm);

    std::string get_debug_save_path(const char *name);

    bool is_enabled_debug();

    bool is_enabled_log();

    void log_stats(const char *fmt, ...);

    // 新增方法：设置日志回调函数
    void set_log_print_func(LogCallback log_print);

    ~Logger() = default;

    Logger(const Logger &) = delete;

    Logger &operator=(const Logger &) = delete;

  public:
    LogCallback log_print_ = nullptr;
    bool enable_log_{false};
    bool enable_debug_{false};
    std::string debug_dir_;
    std::int32_t debug_idx_{0};
    std::stack<std::int64_t> timer_stack_;
};

} // namespace pgrawcvt

#endif // RAW_CONVERSION_RAW_CONVERSION_LOGGER_HPP_
