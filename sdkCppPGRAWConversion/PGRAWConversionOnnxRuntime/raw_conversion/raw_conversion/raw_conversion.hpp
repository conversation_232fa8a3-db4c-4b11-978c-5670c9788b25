//
//  PGRAWConversion
//
//  Created by Tanqy on 2024/05/30.
//  Copyright © 2024 Tanqy. All rights reserved.
//

#ifndef RAW_CONVERSION_RAW_CONVERSION_RAW_CONVERSION_HPP_
#define RAW_CONVERSION_RAW_CONVERSION_RAW_CONVERSION_HPP_

#include <cmath>  // 引入数学库
#include <memory> // 引入智能指针相关库
#include <string> // 引入字符串操作库
#include <vector> // 引入向量容器库

#include <opencv2/opencv.hpp>

#include "onnxruntime_cxx_api.h"

#ifndef PGRAWCVT_EXPORT
#if defined _WIN32 || defined __CYGWIN__
#define PGRAWCVT_EXPORT __declspec(dllexport)
#else
#define PGRAWCVT_EXPORT __attribute__((visibility("default"))) // 定义导出符号的属性为默认可见性
#endif
#endif

/// 定义命名空间pgrawcvt，用于封装RAWConversion类和相关功能，避免命名冲突
namespace pgrawcvt {
/// ONNX 配置
struct PGRAWCVT_EXPORT OrtConfig {
    Ort::AllocatorWithDefaultOptions default_allocator;
    std::shared_ptr<Ort::MemoryInfo> cpu_arena_mem;
    std::shared_ptr<Ort::Env> env;
    Ort::SessionOptions session_opts;
};

/// TODO: 新增的用于存储 4D LUT 调色模型返回的查找表和注意力图数据
struct PGRAWCVT_EXPORT LUTModelOutput {
    cv::Mat luts = cv::Mat();        ///< 查找表数据的智能指针
    std::vector<int64_t> luts_shape; ///< 查找表数据的形状维度
    cv::Mat maps = cv::Mat();        ///< 注意力图数据智能指针
    std::vector<int64_t> maps_shape; ///< 注意力图数据形状维度
};

/// 模型的配置
struct PGRAWCVT_EXPORT RAWCVTConfig {
    bool enable_raw_lut; // 布尔变量，表示是否启用调亮
    bool enable_denoise; // 布尔变量，表示是否启用去噪模型
    bool enable_std_lut; // 布尔变量，表示是否启用调标准色模型
    enum adjust_type {   // 定义一个枚举类型，用于选择调标准色的模式
        AUTO,            // 枚举值AUTO，表示自动选择标准
        STYLE,           // 枚举值STYLE，表示风格化调标准色
        NORMAL           // 枚举值NORMAL，表示一般标准化调色
    } adjustType;        // 枚举变量，存储调色模式的选择
    int n_threads;       // 整数变量，表示用于运算的线程数量；-1表示使用所有可用线程

    // 构造函数，允许在创建RAWCVTConfig对象时初始化所有成员变量，参数带有默认值，因此也支持部分参数初始化，未指定的参数将使用默认值
    RAWCVTConfig(bool enable_raw_lut = true, // 默认启用调亮
                 bool enable_denoise = true, // 默认启用去噪
                 bool enable_std_lut = true, // 默认启用调标准色
                 adjust_type type = AUTO,    // 默认调色模式为: AUTO
                 int threads = -1)           // 默认使用所有线程
        : enable_raw_lut(enable_raw_lut),    // 初始化列表，用于赋值成员变量enable_raw_lut
          enable_denoise(enable_denoise),    // 初始化列表，用于赋值成员变量enable_denoise
          enable_std_lut(enable_std_lut),    // 初始化列表，用于赋值成员变量enable_std_lut
          adjustType(type),                  // 初始化列表，用于赋值成员变量adjustType
          n_threads(threads) {}              // 初始化列表，用于赋值成员变量n_threads
};

/// 算法接口类
class PGRAWCVT_EXPORT RAWConversion {
  public:
    RAWConversion();  // 构造函数声明
    ~RAWConversion(); // 析构函数声明

    /// \brief 初始化
    ///
    /// 初始化
    /// \param ort_config onnxruntime的配置文件
    /// \param key 权限管理系统需要的密钥
    /// \param user_code 权限管理系统需要的用户编码
    /// \param prod_code 权限管理系统需要的产品编码
    /// \param enable_opengl 是否启用OpenGL加速4DLUT计算过程
    /// \return 成功返回 true，失败返回 false.
    bool init(const OrtConfig *ort_config, const std::string &key, const std::string &user_code, const std::string &prod_code, bool enable_opengl = false);

    /// \brief 执行算法
    ///
    /// 传入的 BGR 格式的 16bit 图像，并返回与输入尺寸相同的 BGR 16bit 标准片。
    /// \param config RAWCVTConfig 形式的配置
    /// \param src_image cv::Mat 形式的输入图像
    /// \param dst_image cv::Mat 形式的输出图像
    /// \param src_color_format 支持此四种模式: [1] COLOR_RGB | [2] COLOR_BGR | [4] COLOR_RGBA | [5] COLOR_BGRA
    /// \param adjust_mode 返回内部调色分类器结果: 0 代表标准(默认), 1 代表风格
    /// \param adjust_custom false 走RAW解码流程; true 走JPG调标准色流程
    /// \return 算法处理后的结果，是一个指向单通道图像数据的智能指针.
    bool run(const RAWCVTConfig &config, const cv::Mat &src_image, cv::Mat &dst_image, std::uint32_t src_color_format = 2, int &adjust_mode = *(new int(0)), bool adjust_custom = false);

    /// \brief 获取曝光LUT
    ///
    /// 获取当前使用的曝光LUT数据
    /// \param config RAWCVTConfig 形式的配置
    /// \param src_image cv::Mat 形式的输入图像
    /// \param src_color_format 支持此四种模式: [1] COLOR_RGB | [2] COLOR_BGR | [4] COLOR_RGBA | [5] COLOR_BGRA
    /// \param output 输出参数，用于存储获取到的RAW LUT数据
    /// \return 成功返回 true，失败返回 false
    bool getRawLUT(const RAWCVTConfig &config, const cv::Mat &src_image, LUTModelOutput &output, std::uint32_t src_color_format = 2);

    /// \brief 获取调色LUT
    ///
    /// 获取当前使用的调色LUT数据
    /// \param config RAWCVTConfig 形式的配置
    /// \param src_image cv::Mat 形式的输入图像
    /// \param src_color_format 支持此四种模式: [1] COLOR_RGB | [2] COLOR_BGR | [4] COLOR_RGBA | [5] COLOR_BGRA
    /// \param adjust_mode 返回内部调色分类器结果: 0 代表标准(默认), 1 代表风格
    /// \param adjust_custom false 走RAW解码流程; true 走JPG调标准色流程
    /// \param output 输出参数，用于存储获取到的标准色LUT数据
    /// \return 成功返回 true，失败返回 false
    bool getStdLUT(const RAWCVTConfig &config, const cv::Mat &src_image, LUTModelOutput &output, std::uint32_t src_color_format = 2, int &adjust_mode = *(new int(0)), bool adjust_custom = false);

    /// \brief 清理类
    ///
    /// 清理算法资源，用于在算法使用完毕后释放占用的资源
    /// \return
    void clear();

  private:
    class RAWConversionInner;                   // 声明一个私有内部类，用于实现细节隐藏
    std::unique_ptr<RAWConversionInner> inner_; // 使用智能指针管理RAWConversionInner实例，确保资源正确释放
};

} // namespace pgrawcvt

#endif // RAW_CONVERSION_RAW_CONVERSION_RAW_CONVERSION_HPP_
