#include "apply_ogl.hpp"
#include <cmath>
#include <iostream>
#include <opencv2/opencv.hpp>
#include <string>
#include <type_traits>
#include <vector>

#ifdef USE_OPENGL
// 平台宏定义
#if defined(__APPLE__)
#include <TargetConditionals.h>
#if TARGET_OS_IPHONE
#include <OpenGLES/ES3/gl.h>
#include <OpenGLES/ES3/glext.h>
#else
#include <OpenGL/gl3.h>
#endif
#elif defined(__ANDROID__)
// #include <OpenGLES/ES3/gl.h>
// #include <OpenGLES/ES3/glext.h>
#include <GLES3/gl3.h>
#include <GLES3/gl3ext.h>

#elif defined(_WIN32) || defined(__linux__)
#include <GL/glew.h>
#endif

#endif

namespace pgrawcvt {

#if defined USE_OPENGL

void checkGLError(const std::string &label) {
    GLenum error;
    while ((error = glGetError()) != GL_NO_ERROR) {
        std::cerr << "OpenGL Error (" << label << "): ";
        switch (error) {
        case GL_INVALID_ENUM:
            std::cerr << "GL_INVALID_ENUM";
            break;
        case GL_INVALID_VALUE:
            std::cerr << "GL_INVALID_VALUE";
            break;
        case GL_INVALID_OPERATION:
            std::cerr << "GL_INVALID_OPERATION";
            break;
            //            case GL_STACK_OVERFLOW:
            //                std::cerr << "GL_STACK_OVERFLOW";
            //                break;
            //            case GL_STACK_UNDERFLOW:
            //                std::cerr << "GL_STACK_UNDERFLOW";
            //                break;
        case GL_OUT_OF_MEMORY:
            std::cerr << "GL_OUT_OF_MEMORY";
            break;
        case GL_INVALID_FRAMEBUFFER_OPERATION:
            std::cerr << "GL_INVALID_FRAMEBUFFER_OPERATION";
            break;
        default:
            std::cerr << "Unknown error code: " << error;
            break;
        }
        std::cerr << std::endl;
    }
}

// 创建Framebuffer对象 (FBO)
bool createFramebuffer(GLuint &fbo, GLuint outputTexture, int width, int height) {

    glGenFramebuffers(1, &fbo);
    glBindFramebuffer(GL_FRAMEBUFFER, fbo);

    glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0, GL_TEXTURE_2D, outputTexture, 0);

    auto ret = glCheckFramebufferStatus(GL_FRAMEBUFFER);

    if (ret != GL_FRAMEBUFFER_COMPLETE) {
        std::cerr << "Framebuffer is not complete! ret=" << ret << std::endl;
        glDeleteFramebuffers(1, &fbo);
        fbo = 0;
        return false;
    }

    glBindFramebuffer(GL_FRAMEBUFFER, 0);
    return true;
}

// 加载纹理
GLuint createTexture2D(const void *data, int width, int height, GLenum internalFormat, GLenum format, GLenum type, bool filter_nearest = false) {
    GLuint texture;
    glGenTextures(1, &texture);

    glBindTexture(GL_TEXTURE_2D, texture);

    glTexImage2D(GL_TEXTURE_2D, 0, internalFormat, width, height, 0, format, type, data);

    if (filter_nearest) {
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_NEAREST);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_NEAREST);
    } else {

        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    }

    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
    return texture;
}
GLuint createTexture2DArray(const void *data, int numLayers, int width, int height, GLenum internalFormat, GLenum format, GLenum type) {
    GLuint texture;
    glGenTextures(1, &texture);
    glBindTexture(GL_TEXTURE_2D_ARRAY, texture);

    glTexImage3D(GL_TEXTURE_2D_ARRAY, 0, internalFormat, width, height, numLayers, 0, format, type, data);
    // 设置纹理参数
    glTexParameteri(GL_TEXTURE_2D_ARRAY, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D_ARRAY, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D_ARRAY, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D_ARRAY, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);

    return texture;
}

GLuint createTexture3D(const void *data, int width, int height, int depth, GLenum internalFormat, GLenum format, GLenum type) {
    GLuint texture;
    glGenTextures(1, &texture);
    glBindTexture(GL_TEXTURE_3D, texture);
    glTexImage3D(GL_TEXTURE_3D, 0, internalFormat, width, height, depth, 0, format, type, data);
    glTexParameteri(GL_TEXTURE_3D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_3D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_3D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_3D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_3D, GL_TEXTURE_WRAP_R, GL_CLAMP_TO_EDGE);
    return texture;
}

#ifdef GL_ES_VERSION_3_0
const std::string opengl_version_str = "#version 300 es\n";
#else
const std::string opengl_version_str = "#version 410\n";
#endif

// 创建Shader Program
const std::string vertexShaderSource = opengl_version_str + R"(
    layout(location = 0) in vec2 aPos;
    layout(location = 1) in vec2 aTexCoord;
    out vec2 TexCoord;
    void main() {
        gl_Position = vec4(aPos, 0.0, 1.0);
        TexCoord = aTexCoord;
    }
    )";

std::string xor_encrypt_decrypt(std::string &input, const std::string &key, bool b_encrypt = false) {
    std::string output = input;
    for (size_t i = 0; i < input.size(); ++i) {
        output[i] = input[i] ^ key[i % key.size()]; // 按位异或操作
    }
    if (b_encrypt) {
        size_t length = output.size();

        std::cout << "en:\n";
        for (size_t i = 0; i < length; ++i) {
            printf("%#x,", output[i]);
        }
        std::cout << std::endl;
    }

    return output;
}

// #define SHADER_DEBUG
#ifdef SHADER_DEBUG
std::string g_fragmentShaderGlesU8U16 = R"(
precision highp float;
precision highp sampler2DArray;
precision highp usampler2D;

in vec2 TexCoord;

out uvec4 FragColor;

#define M {M}
#define DIM {DIM}
#define PIXEL_RANGE_MAX {PIXEL_RANGE_MAX}
#define PIXEL_RANGE_SCALE {PIXEL_RANGE_SCALE}

uniform usampler2D inputTexture;
uniform sampler2DArray lutTextures;
uniform sampler2DArray mapTextures;
void main() {
    vec3 inputColor = vec3(texture(inputTexture, TexCoord).rgb) * PIXEL_RANGE_SCALE;
    vec3 lutCoord;
    lutCoord.xy = (inputColor.xy * (DIM - 1.0) + 0.5)/ DIM;
    lutCoord.z = inputColor.z;

    vec3 outputColor = vec3(0.0);

    for(int i = 0; i < M; ++i)
    {
        float layer = float(i) * DIM + lutCoord.z * (DIM - 1.0);

        int layer_idx = int(layer);
        float alpha = layer - float(layer_idx);
        vec3 lutColor0 = texture(lutTextures, vec3(lutCoord.xy,layer_idx)).rgb;
        vec3 lutColor1 = texture(lutTextures, vec3(lutCoord.xy,layer_idx + 1)).rgb;

        float weight = texture(mapTextures, vec3(TexCoord,i)).r;

        vec3 lutColor = lutColor0 + (lutColor1 - lutColor0)* alpha;

        outputColor += lutColor * weight;
    }
    outputColor = clamp(outputColor, 0.0, 1.0)*PIXEL_RANGE_MAX;
    uvec3 outputColoru = uvec3(outputColor);
    FragColor = uvec4(outputColor,1);
}
)";
std::string g_fragmentShaderGlesF32 = R"(
precision highp float;
precision highp sampler2DArray;

in vec2 TexCoord;

out vec4 FragColor;

#define M {M}
#define DIM {DIM}


uniform sampler2D inputTexture;
uniform sampler2DArray lutTextures;
uniform sampler2DArray mapTextures;
void main() {
    vec3 inputColor = texture(inputTexture, TexCoord).rgb;
    vec3 lutCoord;
    lutCoord.xy = (inputColor.xy * (DIM - 1.0) + 0.5)/ DIM;
    lutCoord.z = inputColor.z;

    vec3 outputColor = vec3(0.0);

    for(int i = 0; i < M; ++i)
    {
        float layer = float(i) * DIM + lutCoord.z * (DIM - 1.0);
        int layer_idx = int(layer);
        float alpha = layer - float(layer_idx);
        vec3 lutColor0 = texture(lutTextures,vec3(lutCoord.xy,layer_idx)).rgb; 
        vec3 lutColor1 = texture(lutTextures, vec3(lutCoord.xy,layer_idx + 1)).rgb;

        float weight = texture(mapTextures, vec3(TexCoord,i)).r;

        vec3 lutColor = lutColor0 + (lutColor1 - lutColor0)* alpha;

        outputColor += lutColor * weight;
    }

    outputColor = clamp(outputColor, 0.0, 1.0);
    FragColor = vec4(outputColor, 1.0);
}
)";

std::string g_fragmentShaderGl = R"(
precision highp float;

in vec2 TexCoord;

out vec4 FragColor;

#define M {M}
#define DIM {DIM}


uniform sampler2D inputTexture;
uniform sampler2DArray lutTextures;
uniform sampler2DArray mapTextures;
void main() {
    vec3 inputColor = texture(inputTexture, TexCoord).rgb;
    vec3 lutCoord;
    lutCoord.xy = (inputColor.xy * (DIM - 1.0) + 0.5)/ DIM;
    lutCoord.z = inputColor.z;

    vec3 outputColor = vec3(0.0);

    for(int i = 0; i < M; ++i)
    {
        float layer = i * DIM + lutCoord.z * (DIM - 1);
        int layer_idx = int(layer);
        float alpha = layer - layer_idx;
        vec3 lutColor0 = texture(lutTextures,vec3(lutCoord.xy,layer_idx)).rgb; 
        vec3 lutColor1 = texture(lutTextures, vec3(lutCoord.xy,layer_idx + 1)).rgb;

        float weight = texture(mapTextures, vec3(TexCoord,i)).r;

        vec3 lutColor = lutColor0 + (lutColor1 - lutColor0)* alpha;

        outputColor += lutColor * weight;
    }

    outputColor = clamp(outputColor, 0.0, 1.0);
    FragColor = vec4(outputColor, 1.0);
}
)";
#else
// 解密Shader代码
#ifdef GL_ES_VERSION_3_0
const char g_frcGlesU8U16[] = {
    0x68, 0x2,  0x1d, 0x3a, 0xf,  0xb,  0x1,  0x6,  0x30, 0x2,  0x42, 0x1a, 0x6,  0x38, 0x4,  0x12, 0x52, 0x9,  0x33, 0x3,  0x3,  0x6,  0x54, 0x55, 0x1c, 0x10, 0x17, 0xc,  0x36, 0x1f, 0xb,  0x1d, 0x1,  0x7f, 0x4,  0xb,  0x15, 0x7,  0x2f, 0x4c, 0x11,
    0x13, 0x2,  0x2f, 0,    0x7,  0,    0x5d, 0x1b, 0x2d, 0x10, 0,    0xe,  0x26, 0x57, 0x68, 0x2,  0x1d, 0x3a, 0xf,  0xb,  0x1,  0x6,  0x30, 0x2,  0x42, 0x1a, 0x6,  0x38, 0x4,  0x12, 0x52, 0x1a, 0x2c, 0xd,  0xf,  0x2,  0x3,  0x3a, 0x1e, 0x50, 0x36,
    0x54, 0x55, 0x66, 0xb,  0x1c, 0x4f, 0x29, 0x9,  0x1,  0x40, 0x4f, 0xb,  0x9,  0x1a, 0x31, 0,    0x30, 0x1e, 0x6,  0x49, 0x65, 0x55, 0x3,  0x17, 0x6,  0x4f, 0x2a, 0x1a, 0x7,  0x11, 0x5b, 0x7f, 0x2a, 0x10, 0x13, 0x8,  0x1c, 0x3,  0xe,  0x1d, 0x1d,
    0x64, 0x66, 0x68, 0x51, 0xb,  0x3a, 0xa,  0xb,  0x1c, 0xa,  0x7f, 0x21, 0x42, 0x9,  0x22, 0x22, 0x66, 0x41, 0x16, 0xa,  0x39, 0x5,  0xc,  0x17, 0x4f, 0x1b, 0x25, 0x2f, 0x52, 0x14, 0x1b, 0x25, 0x2f, 0xf,  0x65, 0x7c, 0x8,  0x7,  0x14, 0x6,  0x31,
    0x9,  0x42, 0x22, 0x26, 0x7,  0x29, 0x2e, 0x2d, 0x3d, 0x1e, 0x22, 0x25, 0x37, 0x30, 0x12, 0x2d, 0x3a, 0x52, 0x14, 0xf,  0x25, 0x3a, 0x37, 0x23, 0,    0x3e, 0x23, 0x3c, 0x28, 0x1a, 0x33, 0x2f, 0x33, 0x37, 0x22, 0x66, 0x41, 0x16, 0xa,  0x39, 0x5,
    0xc,  0x17, 0x4f, 0xf,  0x25, 0x3a, 0x37, 0x23, 0,    0x3e, 0x23, 0x3c, 0x28, 0x1a, 0x33, 0x31, 0x31, 0x2e, 0x13, 0x29, 0x42, 0x9,  0x3f, 0x16, 0x34, 0x27, 0x3e, 0x30, 0xd,  0x2d, 0x2c, 0x35, 0x2a, 0,    0x3f, 0x21, 0x33, 0x23, 0x1a, 0x11, 0x68,
    0x78, 0x1a, 0x31, 0x5,  0x4,  0x1d, 0x1d, 0x32, 0x4c, 0x17, 0x1,  0xe,  0x32, 0x1c, 0xe,  0x17, 0x1d, 0x6d, 0x28, 0x42, 0x1b, 0x1,  0x2f, 0x19, 0x16, 0x26, 0xa,  0x27, 0x18, 0x17, 0,    0xa,  0x64, 0x66, 0x17, 0x1c, 0x6,  0x39, 0x3,  0x10, 0x1f,
    0x4f, 0x2c, 0xd,  0xf,  0x2,  0x3,  0x3a, 0x1e, 0x50, 0x36, 0x2e, 0x2d, 0x1e, 0x3,  0xb,  0x4f, 0x33, 0x19, 0x16, 0x26, 0xa,  0x27, 0x18, 0x17, 0,    0xa,  0x2c, 0x57, 0x68, 0x7,  0x1,  0x36, 0xa,  0xd,  0,    0x2,  0x7f, 0x1f, 0x3,  0x1f, 0x1f,
    0x33, 0x9,  0x10, 0x40, 0x2b, 0x1e, 0x1e, 0x10, 0x13, 0x16, 0x7f, 0x1,  0x3,  0x2,  0x3b, 0x3a, 0x14, 0x16, 0x7,  0x1d, 0x3a, 0x1f, 0x59, 0x78, 0x19, 0x30, 0x5,  0x6,  0x52, 0x2,  0x3e, 0x5,  0xc,  0x5a, 0x46, 0x7f, 0x17, 0x68, 0x52, 0x4f, 0x7f,
    0x4c, 0x14, 0x17, 0xc,  0x6c, 0x4c, 0xb,  0x1c, 0x1f, 0x2a, 0x18, 0x21, 0x1d, 0x3,  0x30, 0x1e, 0x42, 0x4f, 0x4f, 0x29, 0x9,  0x1,  0x41, 0x47, 0x2b, 0x9,  0x1a, 0x6,  0x1a, 0x2d, 0x9,  0x4a, 0x1b, 0x1,  0x2f, 0x19, 0x16, 0x26, 0xa,  0x27, 0x18,
    0x17, 0,    0xa,  0x73, 0x4c, 0x36, 0x17, 0x17, 0x1c, 0x3,  0xd,  0,    0xb,  0x76, 0x42, 0x10, 0x15, 0xd,  0x76, 0x4c, 0x48, 0x52, 0x3f, 0x16, 0x34, 0x27, 0x3e, 0x30, 0xd,  0x2d, 0x2c, 0x35, 0x2a, 0,    0x3f, 0x21, 0x33, 0x23, 0x1a, 0x57, 0x68,
    0x52, 0x4f, 0x7f, 0x4c, 0x14, 0x17, 0xc,  0x6c, 0x4c, 0xe,  0x7,  0x1b, 0x1c, 0x3,  0xd,  0,    0xb,  0x64, 0x66, 0x42, 0x52, 0x4f, 0x7f, 0,    0x17, 0x6,  0x2c, 0x30, 0x3,  0x10, 0x16, 0x41, 0x27, 0x15, 0x42, 0x4f, 0x4f, 0x77, 0x5,  0xc,  0x2,
    0x1a, 0x2b, 0x2f, 0xd,  0x1e, 0,    0x2d, 0x42, 0x1a, 0xb,  0x4f, 0x75, 0x4c, 0x4a, 0x36, 0x26, 0x12, 0x4c, 0x4f, 0x52, 0x5e, 0x71, 0x5c, 0x4b, 0x52, 0x44, 0x7f, 0x5c, 0x4c, 0x47, 0x46, 0x70, 0x4c, 0x26, 0x3b, 0x22, 0x64, 0x66, 0x42, 0x52, 0x4f,
    0x7f, 0,    0x17, 0x6,  0x2c, 0x30, 0x3,  0x10, 0x16, 0x41, 0x25, 0x4c, 0x5f, 0x52, 0x6,  0x31, 0x1c, 0x17, 0x6,  0x2c, 0x30, 0,    0xd,  0,    0x41, 0x25, 0x57, 0x68, 0x78, 0x4f, 0x7f, 0x4c, 0x42, 0x4,  0xa,  0x3c, 0x5f, 0x42, 0x1d, 0x1a, 0x2b,
    0x1c, 0x17, 0x6,  0x2c, 0x30, 0,    0xd,  0,    0x4f, 0x62, 0x4c, 0x14, 0x17, 0xc,  0x6c, 0x44, 0x52, 0x5c, 0x5f, 0x76, 0x57, 0x68, 0x78, 0x4f, 0x7f, 0x4c, 0x42, 0x14, 0,    0x2d, 0x44, 0xb,  0x1c, 0x1b, 0x7f, 0x5,  0x42, 0x4f, 0x4f, 0x6f, 0x57,
    0x42, 0x1b, 0x4f, 0x63, 0x4c, 0x2f, 0x49, 0x4f, 0x74, 0x47, 0xb,  0x5b, 0x65, 0x7f, 0x4c, 0x42, 0x52, 0x14, 0x55, 0x4c, 0x42, 0x52, 0x4f, 0x7f, 0x4c, 0x42, 0x52, 0x9,  0x33, 0x3,  0x3,  0x6,  0x4f, 0x33, 0xd,  0x1b, 0x17, 0x1d, 0x7f, 0x51, 0x42,
    0x14, 0x3,  0x30, 0xd,  0x16, 0x5a, 0x6,  0x76, 0x4c, 0x48, 0x52, 0x2b, 0x16, 0x21, 0x42, 0x59, 0x4f, 0x33, 0x19, 0x16, 0x31, 0,    0x30, 0x1e, 0x6,  0x5c, 0x15, 0x7f, 0x46, 0x42, 0x5a, 0x2b, 0x16, 0x21, 0x42, 0x5f, 0x4f, 0x6e, 0x42, 0x52, 0x5b,
    0x54, 0x55, 0x66, 0x42, 0x52, 0x4f, 0x7f, 0x4c, 0x42, 0x52, 0x4f, 0x36, 0x2,  0x16, 0x52, 0x3,  0x3e, 0x15, 0x7,  0,    0x30, 0x36, 0x8,  0x1a, 0x52, 0x52, 0x7f, 0x5,  0xc,  0x6,  0x47, 0x33, 0xd,  0x1b, 0x17, 0x1d, 0x76, 0x57, 0x68, 0x52, 0x4f,
    0x7f, 0x4c, 0x42, 0x52, 0x4f, 0x7f, 0xa,  0xe,  0x1d, 0xe,  0x2b, 0x4c, 0x3,  0x1e, 0x1f, 0x37, 0xd,  0x42, 0x4f, 0x4f, 0x33, 0xd,  0x1b, 0x17, 0x1d, 0x7f, 0x41, 0x42, 0x14, 0x3,  0x30, 0xd,  0x16, 0x5a, 0x3,  0x3e, 0x15, 0x7,  0,    0x30, 0x36,
    0x8,  0x1a, 0x5b, 0x54, 0x55, 0x4c, 0x42, 0x52, 0x4f, 0x7f, 0x4c, 0x42, 0x52, 0x19, 0x3a, 0xf,  0x51, 0x52, 0x3,  0x2a, 0x18, 0x21, 0x1d, 0x3,  0x30, 0x1e, 0x52, 0x52, 0x52, 0x7f, 0x18, 0x7,  0xa,  0x1b, 0x2a, 0x1e, 0x7,  0x5a, 0x3,  0x2a, 0x18,
    0x36, 0x17, 0x17, 0x2b, 0x19, 0x10, 0x17, 0x1c, 0x73, 0x4c, 0x14, 0x17, 0xc,  0x6c, 0x44, 0xe,  0x7,  0x1b, 0x1c, 0x3,  0xd,  0,    0xb,  0x71, 0x14, 0x1b, 0x5e, 0x3,  0x3e, 0x15, 0x7,  0,    0x30, 0x36, 0x8,  0x1a, 0x5b, 0x46, 0x71, 0x1e, 0x5,
    0x10, 0x54, 0x55, 0x4c, 0x42, 0x52, 0x4f, 0x7f, 0x4c, 0x42, 0x52, 0x19, 0x3a, 0xf,  0x51, 0x52, 0x3,  0x2a, 0x18, 0x21, 0x1d, 0x3,  0x30, 0x1e, 0x53, 0x52, 0x52, 0x7f, 0x18, 0x7,  0xa,  0x1b, 0x2a, 0x1e, 0x7,  0x5a, 0x3,  0x2a, 0x18, 0x36, 0x17,
    0x17, 0x2b, 0x19, 0x10, 0x17, 0x1c, 0x73, 0x4c, 0x14, 0x17, 0xc,  0x6c, 0x44, 0xe,  0x7,  0x1b, 0x1c, 0x3,  0xd,  0,    0xb,  0x71, 0x14, 0x1b, 0x5e, 0x3,  0x3e, 0x15, 0x7,  0,    0x30, 0x36, 0x8,  0x1a, 0x52, 0x44, 0x7f, 0x5d, 0x4b, 0x5b, 0x41,
    0x2d, 0xb,  0,    0x49, 0x65, 0x55, 0x4c, 0x42, 0x52, 0x4f, 0x7f, 0x4c, 0x42, 0x52, 0x9,  0x33, 0x3,  0x3,  0x6,  0x4f, 0x28, 0x9,  0xb,  0x15, 0x7,  0x2b, 0x4c, 0x5f, 0x52, 0x1b, 0x3a, 0x14, 0x16, 0x7,  0x1d, 0x3a, 0x44, 0xf,  0x13, 0x1f, 0xb,
    0x9,  0x1a, 0x6,  0x1a, 0x2d, 0x9,  0x11, 0x5e, 0x4f, 0x29, 0x9,  0x1,  0x41, 0x47, 0xb,  0x9,  0x1a, 0x31, 0,    0x30, 0x1e, 0x6,  0x5e, 0x6,  0x76, 0x45, 0x4c, 0,    0x54, 0x55, 0x66, 0x42, 0x52, 0x4f, 0x7f, 0x4c, 0x42, 0x52, 0x4f, 0x29, 0x9,
    0x1,  0x41, 0x4f, 0x33, 0x19, 0x16, 0x31, 0,    0x33, 0x3,  0x10, 0x52, 0x52, 0x7f, 0,    0x17, 0x6,  0x2c, 0x30, 0,    0xd,  0,    0x5f, 0x7f, 0x47, 0x42, 0x5a, 0x3,  0x2a, 0x18, 0x21, 0x1d, 0x3,  0x30, 0x1e, 0x53, 0x52, 0x42, 0x7f, 0,    0x17,
    0x6,  0x2c, 0x30, 0,    0xd,  0,    0x5f, 0x76, 0x46, 0x42, 0x13, 0x3,  0x2f, 0x4,  0x3,  0x49, 0x65, 0x55, 0x4c, 0x42, 0x52, 0x4f, 0x7f, 0x4c, 0x42, 0x52, 0,    0x2a, 0x18, 0x12, 0x7,  0x1b, 0x1c, 0x3,  0xe,  0x1d, 0x1d, 0x7f, 0x47, 0x5f, 0x52,
    0x3,  0x2a, 0x18, 0x21, 0x1d, 0x3,  0x30, 0x1e, 0x42, 0x58, 0x4f, 0x28, 0x9,  0xb,  0x15, 0x7,  0x2b, 0x57, 0x68, 0x52, 0x4f, 0x7f, 0x4c, 0x1f, 0x78, 0x4f, 0x7f, 0x4c, 0x42, 0x1d, 0x1a, 0x2b, 0x1c, 0x17, 0x6,  0x2c, 0x30, 0,    0xd,  0,    0x4f,
    0x62, 0x4c, 0x1,  0x1e, 0xe,  0x32, 0x1c, 0x4a, 0x1d, 0x1a, 0x2b, 0x1c, 0x17, 0x6,  0x2c, 0x30, 0,    0xd,  0,    0x43, 0x7f, 0x5c, 0x4c, 0x42, 0x43, 0x7f, 0x5d, 0x4c, 0x42, 0x46, 0x75, 0x3c, 0x2b, 0x2a, 0x2a, 0x13, 0x33, 0x30, 0x33, 0x21, 0x18,
    0x29, 0x3d, 0x3f, 0x2e, 0x7,  0x57, 0x68, 0x52, 0x4f, 0x7f, 0x4c, 0x17, 0x4,  0xa,  0x3c, 0x5f, 0x42, 0x1d, 0x1a, 0x2b, 0x1c, 0x17, 0x6,  0x2c, 0x30, 0,    0xd,  0,    0x1a, 0x7f, 0x51, 0x42, 0x7,  0x19, 0x3a, 0xf,  0x51, 0x5a, 0,    0x2a, 0x18,
    0x12, 0x7,  0x1b, 0x1c, 0x3,  0xe,  0x1d, 0x1d, 0x76, 0x57, 0x68, 0x52, 0x4f, 0x7f, 0x4c, 0x24, 0,    0xe,  0x38, 0x2f, 0xd,  0x1e, 0,    0x2d, 0x4c, 0x5f, 0x52, 0x1a, 0x29, 0x9,  0x1,  0x46, 0x47, 0x30, 0x19, 0x16, 0x2,  0x1a, 0x2b, 0x2f, 0xd,
    0x1e, 0,    0x2d, 0x40, 0x53, 0x5b, 0x54, 0x55, 0x11, 0x68};
const char g_frcGlesF32[] = {
    0x68, 0x2,  0x1d, 0x3a, 0xf,  0xb,  0x1,  0x6,  0x30, 0x2,  0x42, 0x1a, 0x6,  0x38, 0x4,  0x12, 0x52, 0x9,  0x33, 0x3,  0x3,  0x6,  0x54, 0x55, 0x1c, 0x10, 0x17, 0xc,  0x36, 0x1f, 0xb,  0x1d, 0x1,  0x7f, 0x4,  0xb,  0x15, 0x7,  0x2f, 0x4c,
    0x11, 0x13, 0x2,  0x2f, 0,    0x7,  0,    0x5d, 0x1b, 0x2d, 0x10, 0,    0xe,  0x26, 0x57, 0x68, 0x78, 0x6,  0x31, 0x4c, 0x14, 0x17, 0xc,  0x6d, 0x4c, 0x36, 0x17, 0x17, 0x1c, 0x3,  0xd,  0,    0xb,  0x64, 0x66, 0x68, 0x1d, 0x1a, 0x2b, 0x4c,
    0x14, 0x17, 0xc,  0x6b, 0x4c, 0x24, 0,    0xe,  0x38, 0x2f, 0xd,  0x1e, 0,    0x2d, 0x57, 0x68, 0x78, 0x4c, 0x3b, 0x9,  0x4,  0x1b, 0x1,  0x3a, 0x4c, 0x2f, 0x52, 0x14, 0x12, 0x11, 0x68, 0x51, 0xb,  0x3a, 0xa,  0xb,  0x1c, 0xa,  0x7f, 0x28,
    0x2b, 0x3f, 0x4f, 0x24, 0x28, 0x2b, 0x3f, 0x12, 0x55, 0x66, 0x68, 0x7,  0x1,  0x36, 0xa,  0xd,  0,    0x2,  0x7f, 0x1f, 0x3,  0x1f, 0x1f, 0x33, 0x9,  0x10, 0x40, 0x2b, 0x7f, 0x5,  0xc,  0x2,  0x1a, 0x2b, 0x38, 0x7,  0xa,  0x1b, 0x2a, 0x1e,
    0x7,  0x49, 0x65, 0x2a, 0x2,  0xb,  0x14, 0,    0x2d, 0x1,  0x42, 0x1,  0xe,  0x32, 0x1c, 0xe,  0x17, 0x1d, 0x6d, 0x28, 0x23, 0,    0x1d, 0x3e, 0x15, 0x42, 0x1e, 0x1a, 0x2b, 0x38, 0x7,  0xa,  0x1b, 0x2a, 0x1e, 0x7,  0x1,  0x54, 0x55, 0x19,
    0xc,  0x1b, 0x9,  0x30, 0x1e, 0xf,  0x52, 0x1c, 0x3e, 0x1,  0x12, 0x1e, 0xa,  0x2d, 0x5e, 0x26, 0x33, 0x1d, 0x2d, 0xd,  0x1b, 0x52, 0x2,  0x3e, 0x1c, 0x36, 0x17, 0x17, 0x2b, 0x19, 0x10, 0x17, 0x1c, 0x64, 0x66, 0x14, 0x1d, 0x6,  0x3b, 0x4c,
    0xf,  0x13, 0x6,  0x31, 0x44, 0x4b, 0x52, 0x14, 0x55, 0x4c, 0x42, 0x52, 0x4f, 0x29, 0x9,  0x1,  0x41, 0x4f, 0x36, 0x2,  0x12, 0x7,  0x1b, 0x1c, 0x3,  0xe,  0x1d, 0x1d, 0x7f, 0x51, 0x42, 0x6,  0xa,  0x27, 0x18, 0x17, 0,    0xa,  0x77, 0x5,
    0xc,  0x2,  0x1a, 0x2b, 0x38, 0x7,  0xa,  0x1b, 0x2a, 0x1e, 0x7,  0x5e, 0x4f, 0xb,  0x9,  0x1a, 0x31, 0,    0x30, 0x1e, 0x6,  0x5b, 0x41, 0x2d, 0xb,  0,    0x49, 0x65, 0x7f, 0x4c, 0x42, 0x52, 0x19, 0x3a, 0xf,  0x51, 0x52, 0x3,  0x2a, 0x18,
    0x21, 0x1d, 0,    0x2d, 0x8,  0x59, 0x78, 0x4f, 0x7f, 0x4c, 0x42, 0x1e, 0x1a, 0x2b, 0x2f, 0xd,  0x1d, 0x1d, 0x3b, 0x42, 0x1a, 0xb,  0x4f, 0x62, 0x4c, 0x4a, 0x1b, 0x1,  0x2f, 0x19, 0x16, 0x31, 0,    0x33, 0x3,  0x10, 0x5c, 0x17, 0x26, 0x4c,
    0x48, 0x52, 0x47, 0x1b, 0x25, 0x2f, 0x52, 0x42, 0x7f, 0x5d, 0x4c, 0x42, 0x46, 0x7f, 0x47, 0x42, 0x42, 0x41, 0x6a, 0x45, 0x4d, 0x52, 0x2b, 0x16, 0x21, 0x59, 0x78, 0x4f, 0x7f, 0x4c, 0x42, 0x1e, 0x1a, 0x2b, 0x2f, 0xd,  0x1d, 0x1d, 0x3b, 0x42,
    0x18, 0x52, 0x52, 0x7f, 0x5,  0xc,  0x2,  0x1a, 0x2b, 0x2f, 0xd,  0x1e, 0,    0x2d, 0x42, 0x18, 0x49, 0x65, 0x55, 0x4c, 0x42, 0x52, 0x4f, 0x29, 0x9,  0x1,  0x41, 0x4f, 0x30, 0x19, 0x16, 0x2,  0x1a, 0x2b, 0x2f, 0xd,  0x1e, 0,    0x2d, 0x4c,
    0x5f, 0x52, 0x19, 0x3a, 0xf,  0x51, 0x5a, 0x5f, 0x71, 0x5c, 0x4b, 0x49, 0x65, 0x55, 0x4c, 0x42, 0x52, 0x4f, 0x39, 0x3,  0x10, 0x5a, 0x6,  0x31, 0x18, 0x42, 0x1b, 0x4f, 0x62, 0x4c, 0x52, 0x49, 0x4f, 0x36, 0x4c, 0x5e, 0x52, 0x22, 0x64, 0x4c,
    0x49, 0x59, 0x6,  0x76, 0x66, 0x42, 0x52, 0x4f, 0x7f, 0x17, 0x68, 0x52, 0x4f, 0x7f, 0x4c, 0x42, 0x52, 0x4f, 0x7f, 0xa,  0xe,  0x1d, 0xe,  0x2b, 0x4c, 0xe,  0x13, 0x16, 0x3a, 0x1e, 0x42, 0x4f, 0x4f, 0x39, 0,    0xd,  0x13, 0x1b, 0x77, 0x5,
    0x4b, 0x52, 0x45, 0x7f, 0x28, 0x2b, 0x3f, 0x4f, 0x74, 0x4c, 0xe,  0x7,  0x1b, 0x1c, 0x3,  0xd,  0,    0xb,  0x71, 0x16, 0x42, 0x58, 0x4f, 0x77, 0x28, 0x2b, 0x3f, 0x4f, 0x72, 0x4c, 0x53, 0x5c, 0x5f, 0x76, 0x57, 0x68, 0x52, 0x4f, 0x7f, 0x4c,
    0x42, 0x52, 0x4f, 0x7f, 0x5,  0xc,  0x6,  0x4f, 0x33, 0xd,  0x1b, 0x17, 0x1d, 0,    0x5,  0x6,  0xa,  0x4f, 0x62, 0x4c, 0xb,  0x1c, 0x1b, 0x77, 0,    0x3,  0xb,  0xa,  0x2d, 0x45, 0x59, 0x78, 0x4f, 0x7f, 0x4c, 0x42, 0x52, 0x4f, 0x7f, 0x4c,
    0x4,  0x1e, 0,    0x3e, 0x18, 0x42, 0x13, 0x3,  0x2f, 0x4,  0x3,  0x52, 0x52, 0x7f, 0,    0x3,  0xb,  0xa,  0x2d, 0x4c, 0x4f, 0x52, 0x9,  0x33, 0x3,  0x3,  0x6,  0x47, 0x33, 0xd,  0x1b, 0x17, 0x1d, 0,    0x5,  0x6,  0xa,  0x46, 0x64, 0x66,
    0x42, 0x52, 0x4f, 0x7f, 0x4c, 0x42, 0x52, 0x4f, 0x29, 0x9,  0x1,  0x41, 0x4f, 0x33, 0x19, 0x16, 0x31, 0,    0x33, 0x3,  0x10, 0x42, 0x4f, 0x62, 0x4c, 0x16, 0x17, 0x17, 0x2b, 0x19, 0x10, 0x17, 0x47, 0x33, 0x19, 0x16, 0x26, 0xa,  0x27, 0x18,
    0x17, 0,    0xa,  0x2c, 0x40, 0x14, 0x17, 0xc,  0x6c, 0x44, 0xe,  0x7,  0x1b, 0x1c, 0x3,  0xd,  0,    0xb,  0x71, 0x14, 0x1b, 0x5e, 0x3,  0x3e, 0x15, 0x7,  0,    0x30, 0x36, 0x8,  0x1a, 0x5b, 0x46, 0x71, 0x1e, 0x5,  0x10, 0x54, 0x7f, 0x66,
    0x42, 0x52, 0x4f, 0x7f, 0x4c, 0x42, 0x52, 0x4f, 0x29, 0x9,  0x1,  0x41, 0x4f, 0x33, 0x19, 0x16, 0x31, 0,    0x33, 0x3,  0x10, 0x43, 0x4f, 0x62, 0x4c, 0x16, 0x17, 0x17, 0x2b, 0x19, 0x10, 0x17, 0x47, 0x33, 0x19, 0x16, 0x26, 0xa,  0x27, 0x18,
    0x17, 0,    0xa,  0x2c, 0x40, 0x42, 0x4,  0xa,  0x3c, 0x5f, 0x4a, 0x1e, 0x1a, 0x2b, 0x2f, 0xd,  0x1d, 0x1d, 0x3b, 0x42, 0x1a, 0xb,  0x43, 0x33, 0xd,  0x1b, 0x17, 0x1d, 0,    0x5,  0x6,  0xa,  0x4f, 0x74, 0x4c, 0x53, 0x5b, 0x46, 0x71, 0x1e,
    0x5,  0x10, 0x54, 0x55, 0x66, 0x42, 0x52, 0x4f, 0x7f, 0x4c, 0x42, 0x52, 0x4f, 0x39, 0,    0xd,  0x13, 0x1b, 0x7f, 0x1b, 0x7,  0x1b, 0x8,  0x37, 0x18, 0x42, 0x4f, 0x4f, 0x2b, 0x9,  0x1a, 0x6,  0x1a, 0x2d, 0x9,  0x4a, 0x1f, 0xe,  0x2f, 0x38,
    0x7,  0xa,  0x1b, 0x2a, 0x1e, 0x7,  0x1,  0x43, 0x7f, 0x1a, 0x7,  0x11, 0x5c, 0x77, 0x38, 0x7,  0xa,  0x2c, 0x30, 0x3,  0x10, 0x16, 0x43, 0x36, 0x45, 0x4b, 0x5c, 0x1d, 0x64, 0x66, 0x68, 0x52, 0x4f, 0x7f, 0x4c, 0x42, 0x52, 0x4f, 0x7f, 0x1a,
    0x7,  0x11, 0x5c, 0x7f, 0,    0x17, 0x6,  0x2c, 0x30, 0,    0xd,  0,    0x4f, 0x62, 0x4c, 0xe,  0x7,  0x1b, 0x1c, 0x3,  0xe,  0x1d, 0x1d, 0x6f, 0x4c, 0x49, 0x52, 0x47, 0x33, 0x19, 0x16, 0x31, 0,    0x33, 0x3,  0x10, 0x43, 0x4f, 0x72, 0x4c,
    0xe,  0x7,  0x1b, 0x1c, 0x3,  0xe,  0x1d, 0x1d, 0x6f, 0x45, 0x48, 0x52, 0xe,  0x33, 0x1c, 0xa,  0x13, 0x54, 0x55, 0x66, 0x42, 0x52, 0x4f, 0x7f, 0x4c, 0x42, 0x52, 0x4f, 0x30, 0x19, 0x16, 0x2,  0x1a, 0x2b, 0x2f, 0xd,  0x1e, 0,    0x2d, 0x4c,
    0x49, 0x4f, 0x4f, 0x33, 0x19, 0x16, 0x31, 0,    0x33, 0x3,  0x10, 0x52, 0x45, 0x7f, 0x1b, 0x7,  0x1b, 0x8,  0x37, 0x18, 0x59, 0x78, 0x4f, 0x7f, 0x4c, 0x42, 0xf,  0x65, 0x55, 0x4c, 0x42, 0x52, 0x4f, 0x30, 0x19, 0x16, 0x2,  0x1a, 0x2b, 0x2f,
    0xd,  0x1e, 0,    0x2d, 0x4c, 0x5f, 0x52, 0xc,  0x33, 0xd,  0xf,  0x2,  0x47, 0x30, 0x19, 0x16, 0x2,  0x1a, 0x2b, 0x2f, 0xd,  0x1e, 0,    0x2d, 0x40, 0x42, 0x42, 0x41, 0x6f, 0x40, 0x42, 0x43, 0x41, 0x6f, 0x45, 0x59, 0x78, 0x4f, 0x7f, 0x4c,
    0x42, 0x34, 0x1d, 0x3e, 0xb,  0x21, 0x1d, 0x3,  0x30, 0x1e, 0x42, 0x4f, 0x4f, 0x29, 0x9,  0x1,  0x46, 0x47, 0x30, 0x19, 0x16, 0x2,  0x1a, 0x2b, 0x2f, 0xd,  0x1e, 0,    0x2d, 0x40, 0x42, 0x43, 0x41, 0x6f, 0x45, 0x59, 0x78, 0x12, 0x55};
#else
const char g_frcGl[] = {
    0x68, 0x2,  0x1d, 0x3a, 0xf,  0xb,  0x1,  0x6,  0x30, 0x2,  0x42, 0x1a, 0x6,  0x38, 0x4,  0x12, 0x52, 0x9,  0x33, 0x3,  0x3,  0x6,  0x54, 0x55, 0x66, 0xb,  0x1c, 0x4f, 0x29, 0x9,  0x1,  0x40, 0x4f, 0xb,  0x9,  0x1a, 0x31, 0,    0x30, 0x1e,
    0x6,  0x49, 0x65, 0x55, 0x3,  0x17, 0x6,  0x4f, 0x29, 0x9,  0x1,  0x46, 0x4f, 0x19, 0x1e, 0x3,  0x15, 0x2c, 0x30, 0,    0xd,  0,    0x54, 0x55, 0x66, 0x41, 0x16, 0xa,  0x39, 0x5,  0xc,  0x17, 0x4f, 0x12, 0x4c, 0x19, 0x3f, 0x12, 0x55, 0x4f,
    0x6,  0x17, 0x9,  0x36, 0x2,  0x7,  0x52, 0x2b, 0x16, 0x21, 0x42, 0x9,  0x2b, 0x16, 0x21, 0x1f, 0x78, 0x65, 0x55, 0x19, 0xc,  0x1b, 0x9,  0x30, 0x1e, 0xf,  0x52, 0x1c, 0x3e, 0x1,  0x12, 0x1e, 0xa,  0x2d, 0x5e, 0x26, 0x52, 0x6,  0x31, 0x1c,
    0x17, 0x6,  0x3b, 0x3a, 0x14, 0x16, 0x7,  0x1d, 0x3a, 0x57, 0x68, 0x7,  0x1,  0x36, 0xa,  0xd,  0,    0x2,  0x7f, 0x1f, 0x3,  0x1f, 0x1f, 0x33, 0x9,  0x10, 0x40, 0x2b, 0x1e, 0x1e, 0x10, 0x13, 0x16, 0x7f, 0,    0x17, 0x6,  0x3b, 0x3a, 0x14,
    0x16, 0x7,  0x1d, 0x3a, 0x1f, 0x59, 0x78, 0x1a, 0x31, 0x5,  0x4,  0x1d, 0x1d, 0x32, 0x4c, 0x11, 0x13, 0x2,  0x2f, 0,    0x7,  0,    0x5d, 0x1b, 0x2d, 0x10, 0,    0xe,  0x26, 0x4c, 0xf,  0x13, 0x1f, 0xb,  0x9,  0x1a, 0x6,  0x1a, 0x2d, 0x9,
    0x11, 0x49, 0x65, 0x29, 0x3,  0xb,  0x16, 0x4f, 0x32, 0xd,  0xb,  0x1c, 0x47, 0x76, 0x4c, 0x19, 0x78, 0x4f, 0x7f, 0x4c, 0x42, 0x4,  0xa,  0x3c, 0x5f, 0x42, 0x1b, 0x1,  0x2f, 0x19, 0x16, 0x31, 0,    0x33, 0x3,  0x10, 0x52, 0x52, 0x7f, 0x18,
    0x7,  0xa,  0x1b, 0x2a, 0x1e, 0x7,  0x5a, 0x6,  0x31, 0x1c, 0x17, 0x6,  0x3b, 0x3a, 0x14, 0x16, 0x7,  0x1d, 0x3a, 0x40, 0x42, 0x26, 0xa,  0x27, 0x2f, 0xd,  0x1d, 0x1d, 0x3b, 0x45, 0x4c, 0,    0x8,  0x3d, 0x57, 0x68, 0x52, 0x4f, 0x7f, 0x4c,
    0x14, 0x17, 0xc,  0x6c, 0x4c, 0xe,  0x7,  0x1b, 0x1c, 0x3,  0xd,  0,    0xb,  0x64, 0x66, 0x42, 0x52, 0x4f, 0x7f, 0,    0x17, 0x6,  0x2c, 0x30, 0x3,  0x10, 0x16, 0x41, 0x27, 0x15, 0x42, 0x4f, 0x4f, 0x77, 0x5,  0xc,  0x2,  0x1a, 0x2b, 0x2f,
    0xd,  0x1e, 0,    0x2d, 0x42, 0x1a, 0xb,  0x4f, 0x75, 0x4c, 0x4a, 0x36, 0x26, 0x12, 0x4c, 0x4f, 0x52, 0x5e, 0x71, 0x5c, 0x4b, 0x52, 0x44, 0x7f, 0x5c, 0x4c, 0x47, 0x46, 0x70, 0x4c, 0x26, 0x3b, 0x22, 0x64, 0x66, 0x42, 0x52, 0x4f, 0x7f, 0,
    0x17, 0x6,  0x2c, 0x30, 0x3,  0x10, 0x16, 0x41, 0x25, 0x4c, 0x5f, 0x52, 0x6,  0x31, 0x1c, 0x17, 0x6,  0x2c, 0x30, 0,    0xd,  0,    0x41, 0x25, 0x57, 0x68, 0x78, 0x4f, 0x7f, 0x4c, 0x42, 0x4,  0xa,  0x3c, 0x5f, 0x42, 0x1d, 0x1a, 0x2b, 0x1c,
    0x17, 0x6,  0x2c, 0x30, 0,    0xd,  0,    0x4f, 0x62, 0x4c, 0x14, 0x17, 0xc,  0x6c, 0x44, 0x52, 0x5c, 0x5f, 0x76, 0x57, 0x68, 0x78, 0x4f, 0x7f, 0x4c, 0x42, 0x14, 0,    0x2d, 0x44, 0xb,  0x1c, 0x1b, 0x7f, 0x5,  0x42, 0x4f, 0x4f, 0x6f, 0x57,
    0x42, 0x1b, 0x4f, 0x63, 0x4c, 0x2f, 0x49, 0x4f, 0x74, 0x47, 0xb,  0x5b, 0x65, 0x7f, 0x4c, 0x42, 0x52, 0x14, 0x55, 0x4c, 0x42, 0x52, 0x4f, 0x7f, 0x4c, 0x42, 0x52, 0x9,  0x33, 0x3,  0x3,  0x6,  0x4f, 0x33, 0xd,  0x1b, 0x17, 0x1d, 0x7f, 0x51,
    0x42, 0x1b, 0x4f, 0x75, 0x4c, 0x26, 0x3b, 0x22, 0x7f, 0x47, 0x42, 0x1e, 0x1a, 0x2b, 0x2f, 0xd,  0x1d, 0x1d, 0x3b, 0x42, 0x18, 0x52, 0x45, 0x7f, 0x44, 0x26, 0x3b, 0x22, 0x7f, 0x41, 0x42, 0x43, 0x46, 0x64, 0x66, 0x42, 0x52, 0x4f, 0x7f, 0x4c,
    0x42, 0x52, 0x4f, 0x36, 0x2,  0x16, 0x52, 0x3,  0x3e, 0x15, 0x7,  0,    0x30, 0x36, 0x8,  0x1a, 0x52, 0x52, 0x7f, 0x5,  0xc,  0x6,  0x47, 0x33, 0xd,  0x1b, 0x17, 0x1d, 0x76, 0x57, 0x68, 0x52, 0x4f, 0x7f, 0x4c, 0x42, 0x52, 0x4f, 0x7f, 0xa,
    0xe,  0x1d, 0xe,  0x2b, 0x4c, 0x3,  0x1e, 0x1f, 0x37, 0xd,  0x42, 0x4f, 0x4f, 0x33, 0xd,  0x1b, 0x17, 0x1d, 0x7f, 0x41, 0x42, 0x1e, 0xe,  0x26, 0x9,  0x10, 0x2d, 0x6,  0x3b, 0x14, 0x59, 0x78, 0x4f, 0x7f, 0x4c, 0x42, 0x52, 0x4f, 0x7f, 0x4c,
    0x14, 0x17, 0xc,  0x6c, 0x4c, 0xe,  0x7,  0x1b, 0x1c, 0x3,  0xe,  0x1d, 0x1d, 0x6f, 0x4c, 0x5f, 0x52, 0x1b, 0x3a, 0x14, 0x16, 0x7,  0x1d, 0x3a, 0x44, 0xe,  0x7,  0x1b, 0xb,  0x9,  0x1a, 0x6,  0x1a, 0x2d, 0x9,  0x11, 0x5e, 0x19, 0x3a, 0xf,
    0x51, 0x5a, 0x3,  0x2a, 0x18, 0x21, 0x1d, 0,    0x2d, 0x8,  0x4c, 0xa,  0x16, 0x73, 0,    0x3,  0xb,  0xa,  0x2d, 0x33, 0xb,  0x16, 0x17, 0x76, 0x45, 0x4c, 0,    0x8,  0x3d, 0x57, 0x42, 0x78, 0x4f, 0x7f, 0x4c, 0x42, 0x52, 0x4f, 0x7f, 0x4c,
    0x14, 0x17, 0xc,  0x6c, 0x4c, 0xe,  0x7,  0x1b, 0x1c, 0x3,  0xe,  0x1d, 0x1d, 0x6e, 0x4c, 0x5f, 0x52, 0x1b, 0x3a, 0x14, 0x16, 0x7,  0x1d, 0x3a, 0x44, 0xe,  0x7,  0x1b, 0xb,  0x9,  0x1a, 0x6,  0x1a, 0x2d, 0x9,  0x11, 0x5e, 0x4f, 0x29, 0x9,
    0x1,  0x41, 0x47, 0x33, 0x19, 0x16, 0x31, 0,    0x30, 0x1e, 0x6,  0x5c, 0x17, 0x26, 0x40, 0xe,  0x13, 0x16, 0x3a, 0x1e, 0x3d, 0x1b, 0xb,  0x27, 0x4c, 0x49, 0x52, 0x5e, 0x76, 0x45, 0x4c, 0,    0x8,  0x3d, 0x57, 0x68, 0x78, 0x4f, 0x7f, 0x4c,
    0x42, 0x52, 0x4f, 0x7f, 0x4c, 0x4,  0x1e, 0,    0x3e, 0x18, 0x42, 0x5,  0xa,  0x36, 0xb,  0xa,  0x6,  0x4f, 0x62, 0x4c, 0x16, 0x17, 0x17, 0x2b, 0x19, 0x10, 0x17, 0x47, 0x32, 0xd,  0x12, 0x26, 0xa,  0x27, 0x18, 0x17, 0,    0xa,  0x2c, 0x40,
    0x42, 0x4,  0xa,  0x3c, 0x5f, 0x4a, 0x26, 0xa,  0x27, 0x2f, 0xd,  0x1d, 0x1d, 0x3b, 0x40, 0xb,  0x5b, 0x46, 0x71, 0x1e, 0x59, 0x78, 0x65, 0x7f, 0x4c, 0x42, 0x52, 0x4f, 0x7f, 0x4c, 0x42, 0x4,  0xa,  0x3c, 0x5f, 0x42, 0x1e, 0x1a, 0x2b, 0x2f,
    0xd,  0x1e, 0,    0x2d, 0x4c, 0x5f, 0x52, 0x3,  0x2a, 0x18, 0x21, 0x1d, 0x3,  0x30, 0x1e, 0x52, 0x52, 0x44, 0x7f, 0x44, 0xe,  0x7,  0x1b, 0x1c, 0x3,  0xe,  0x1d, 0x1d, 0x6e, 0x4c, 0x4f, 0x52, 0x3,  0x2a, 0x18, 0x21, 0x1d, 0x3,  0x30, 0x1e,
    0x52, 0x5b, 0x45, 0x7f, 0xd,  0xe,  0x2,  0x7,  0x3e, 0x57, 0x68, 0x78, 0x4f, 0x7f, 0x4c, 0x42, 0x52, 0x4f, 0x7f, 0x4c, 0xd,  0x7,  0x1b, 0x2f, 0x19, 0x16, 0x31, 0,    0x33, 0x3,  0x10, 0x52, 0x44, 0x62, 0x4c, 0xe,  0x7,  0x1b, 0x1c, 0x3,
    0xe,  0x1d, 0x1d, 0x7f, 0x46, 0x42, 0x5,  0xa,  0x36, 0xb,  0xa,  0x6,  0x54, 0x55, 0x4c, 0x42, 0x52, 0x4f, 0x22, 0x66, 0x68, 0x52, 0x4f, 0x7f, 0x4c, 0xd,  0x7,  0x1b, 0x2f, 0x19, 0x16, 0x31, 0,    0x33, 0x3,  0x10, 0x52, 0x52, 0x7f, 0xf,
    0xe,  0x13, 0x2,  0x2f, 0x44, 0xd,  0x7,  0x1b, 0x2f, 0x19, 0x16, 0x31, 0,    0x33, 0x3,  0x10, 0x5e, 0x4f, 0x6f, 0x42, 0x52, 0x5e, 0x4f, 0x6e, 0x42, 0x52, 0x5b, 0x54, 0x55, 0x4c, 0x42, 0x52, 0x4f, 0x19, 0x1e, 0x3,  0x15, 0x2c, 0x30, 0,
    0xd,  0,    0x4f, 0x62, 0x4c, 0x14, 0x17, 0xc,  0x6b, 0x44, 0xd,  0x7,  0x1b, 0x2f, 0x19, 0x16, 0x31, 0,    0x33, 0x3,  0x10, 0x5e, 0x4f, 0x6e, 0x42, 0x52, 0x5b, 0x54, 0x55, 0x11, 0x68};
#endif
#endif

std::string generateFragmentShaderSource(int M, int lut_dim, int elem_type) {
    std::string key = "bro_l"; // 简单的密钥
                               // 基础着色器代码模板
                               // std::string fragmentShaderTemplate0 =  R"(
                               //     precision highp float;

    //     in vec2 TexCoord;

    //     out vec4 FragColor;

    //     #define M {M}
    //     #define DIM {DIM}

    //     uniform sampler2D inputTexture;
    //     uniform sampler3D lutTextures[M];
    //     uniform sampler2D mapTextures[M];

    //     void main() {
    //         vec3 inputColor = texture(inputTexture, TexCoord).rgb;
    //         vec3 lutCoord = (inputColor * (DIM - 1.0) + 0.5)/ DIM;

    //         vec3 outputColor = vec3(0.0);

    //         for(int i = 0; i < M; ++i)
    //         {
    //             vec3 lutColor = texture(lutTextures[i], lutCoord).rgb;
    //             float weight = texture(mapTextures[i], TexCoord).r;
    //             outputColor += lutColor * weight;
    //         }

    //         outputColor = clamp(outputColor, 0.0, 1.0);
    //         FragColor = vec4(outputColor, 1.0);
    //     }
    // )";

#ifdef SHADER_DEBUG
    std::string fragmentShaderTemplate;
#ifdef GL_ES_VERSION_3_0
    if (elem_type == 0) {
        fragmentShaderTemplate = g_fragmentShaderGlesU8U16;
    } else if (elem_type == 1) {
        fragmentShaderTemplate = g_fragmentShaderGlesU8U16;
    } else {
        fragmentShaderTemplate = g_fragmentShaderGlesF32;
    }
#else
    fragmentShaderTemplate = g_fragmentShaderGl;
#endif

    // 加密Shader代码
    std::string encryptedShaderCode = xor_encrypt_decrypt(fragmentShaderTemplate, key, true);
    fragmentShaderTemplate = opengl_version_str + fragmentShaderTemplate;

#else
    std::string encryptedShaderCode;
#ifdef GL_ES_VERSION_3_0
    if (elem_type == 0 || elem_type == 1) {
        encryptedShaderCode = std::string(g_frcGlesU8U16, sizeof(g_frcGlesU8U16));
    } else {
        encryptedShaderCode = std::string(g_frcGlesF32, sizeof(g_frcGlesF32));
    }
#else
    encryptedShaderCode = std::string(g_frcGl, sizeof(g_frcGl));
#endif
    std::string fragmentShaderTemplate = opengl_version_str + xor_encrypt_decrypt(encryptedShaderCode, key);
#endif
    // 替换模板中的<VALUE>为实际的M值
    size_t startPos = fragmentShaderTemplate.find("{M}");
    if (startPos != std::string::npos) {
        fragmentShaderTemplate.replace(startPos, std::string("{M}").size(), std::to_string(M));
    }
    startPos = fragmentShaderTemplate.find("{DIM}");
    if (startPos != std::string::npos) {
        fragmentShaderTemplate.replace(startPos, std::string("{DIM}").size(), std::to_string(lut_dim) + ".f");
    }
#ifdef GL_ES_VERSION_3_0
    float pixel_range;
    if (elem_type == 0) { // u8
        pixel_range = 255.f;
    } else if (elem_type == 1) { // u16
        pixel_range = 65535.f;
    }

    if (elem_type == 0 || elem_type == 1) {
        startPos = fragmentShaderTemplate.find("{PIXEL_RANGE_MAX}");
        if (startPos != std::string::npos) {
            fragmentShaderTemplate.replace(startPos, std::string("{PIXEL_RANGE_MAX}").size(), std::to_string(pixel_range));
        }

        auto scale = 1 / pixel_range;
        startPos = fragmentShaderTemplate.find("{PIXEL_RANGE_SCALE}");
        if (startPos != std::string::npos) {
            fragmentShaderTemplate.replace(startPos, std::string("{PIXEL_RANGE_SCALE}").size(), std::to_string(scale));
        }
    }
#endif

#ifdef SHADER_DEBUG
    std::cout << "frag shader:\n" << fragmentShaderTemplate << std::endl;
#endif
    return fragmentShaderTemplate;
}

bool createShaderProgram(int &shaderProgram, int M, int lut_dim, int elem_type) {
    // 创建顶点着色器
    GLuint vertexShader = glCreateShader(GL_VERTEX_SHADER);
    const char *vertexSourcePointer = vertexShaderSource.c_str();
    glShaderSource(vertexShader, 1, &vertexSourcePointer, nullptr);
    glCompileShader(vertexShader);

    // 检查顶点着色器编译状态
    int success;
    glGetShaderiv(vertexShader, GL_COMPILE_STATUS, &success);
    if (!success) {
        char infoLog[512];
        glGetShaderInfoLog(vertexShader, 512, nullptr, infoLog);
        std::cerr << "ERROR::SHADER::VERTEX::COMPILATION_FAILED\n" << infoLog << std::endl;
    } else {
        // 创建片段着色器
        GLuint fragmentShader = glCreateShader(GL_FRAGMENT_SHADER);
        auto frag_s = generateFragmentShaderSource(M, lut_dim, elem_type);
        const char *sourcePointer = frag_s.c_str();
        glShaderSource(fragmentShader, 1, &sourcePointer, nullptr);
        glCompileShader(fragmentShader);

        // 检查片段着色器编译状态
        glGetShaderiv(fragmentShader, GL_COMPILE_STATUS, &success);
        if (!success) {
            char infoLog[512];
            glGetShaderInfoLog(fragmentShader, 512, nullptr, infoLog);
            std::cerr << "ERROR::SHADER::FRAGMENT::COMPILATION_FAILED\n" << infoLog << std::endl;
        } else {
            // 创建并链接着色器程序
            shaderProgram = glCreateProgram();
            glAttachShader(shaderProgram, vertexShader);
            glAttachShader(shaderProgram, fragmentShader);
            glLinkProgram(shaderProgram);

            // 检查着色器程序链接状态
            glGetProgramiv(shaderProgram, GL_LINK_STATUS, &success);
            if (!success) {
                char infoLog[512];
                glGetProgramInfoLog(shaderProgram, 512, nullptr, infoLog);
                std::cerr << "ERROR::SHADER::PROGRAM::LINKING_FAILED\n" << infoLog << std::endl;
                std::cerr << "ERROR:: shaderProgram:\n" << shaderProgram << std::endl;
                glDeleteProgram(shaderProgram);
                shaderProgram = -1;
            }
        }

        // 删除已不再需要的着色器对象
        glDeleteShader(fragmentShader);
    }

    // 删除已不再需要的着色器对象
    glDeleteShader(vertexShader);

    return success;
}

// 渲染到FBO
void renderToFBO(GLuint fbo, GLuint shaderProgram, GLuint inputTexture, GLuint lutTextures, GLuint mapTextures, int M, int width, int height) {
    glBindFramebuffer(GL_FRAMEBUFFER, fbo);
    glViewport(0, 0, width, height);
    glClearColor(0.0f, 0.0f, 0.0f, 1.0f);
    glClear(GL_COLOR_BUFFER_BIT);

    glUseProgram(shaderProgram);

    // 绑定输入纹理
    glActiveTexture(GL_TEXTURE0);
    glBindTexture(GL_TEXTURE_2D, inputTexture);
    glUniform1i(glGetUniformLocation(shaderProgram, "inputTexture"), 0);

    // 绑定LUT纹理
    auto texidx = GL_TEXTURE1;

    // 绑定单通道map纹理
    {
        glActiveTexture(texidx);
        glBindTexture(GL_TEXTURE_2D_ARRAY, lutTextures);
        glUniform1i(glGetUniformLocation(shaderProgram, "lutTextures"), 1);
        texidx++;

        glActiveTexture(texidx);
        glBindTexture(GL_TEXTURE_2D_ARRAY, mapTextures);
        glUniform1i(glGetUniformLocation(shaderProgram, "mapTextures"), 2);
    }

    // 渲染一个全屏四边形
    GLfloat vertices[] = {-1.0f, -1.0f, 0.0f, 0.0f, 1.0f, -1.0f, 1.0f, 0.0f, -1.0f, 1.0f, 0.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f};

    GLuint vao, vbo;
    glGenVertexArrays(1, &vao);
    glGenBuffers(1, &vbo);
    glBindVertexArray(vao);
    glBindBuffer(GL_ARRAY_BUFFER, vbo);
    glBufferData(GL_ARRAY_BUFFER, sizeof(vertices), vertices, GL_STATIC_DRAW);

    glVertexAttribPointer(0, 2, GL_FLOAT, GL_FALSE, 4 * sizeof(GLfloat), (void *)0);
    glEnableVertexAttribArray(0);
    glVertexAttribPointer(1, 2, GL_FLOAT, GL_FALSE, 4 * sizeof(GLfloat), (void *)(2 * sizeof(GLfloat)));
    glEnableVertexAttribArray(1);

    glDrawArrays(GL_TRIANGLE_STRIP, 0, 4);

    glBindVertexArray(0);
    glBindFramebuffer(GL_FRAMEBUFFER, 0);
}
#endif

bool triLinear_spatial_aware_opengl(int &shaderProgram, const void *psrc_data, void *pdst_data, int width, int height, int elem_type, const float *luts, std::vector<int64_t> &luts_shape, const float *maps, std::vector<int64_t> &maps_shape) {
#if defined USE_OPENGL

    // psrc_data:height,width,3, rgb
    // luts:1,M, (bins,bins,bins,3)
    // maps:1,M,256,256
    // pixel_range_max: U8:255  U16:65535 F32:1
    // elem_type: 0: u8,  1:u16,  2: float32

    GLenum input_texture_internalformat, input_texture_data_type, input_texture_format;
    GLenum output_texture_internalformat, output_texture_data_type, output_texture_format;
    GLint pack_unpack_allignment = 1;
    if (elem_type == 0) {
        // 如果T是uint8_t时执行的代码
#ifdef GL_ES_VERSION_3_0
        input_texture_internalformat = GL_RGB8UI;
        input_texture_format = GL_RGB_INTEGER;
        input_texture_data_type = GL_UNSIGNED_BYTE;

        output_texture_internalformat = GL_RGBA8UI;
        output_texture_format = GL_RGBA_INTEGER;
        output_texture_data_type = GL_UNSIGNED_BYTE;
#else
        input_texture_internalformat = GL_RGB8;
        input_texture_format = GL_RGB;
        input_texture_data_type = GL_UNSIGNED_BYTE;

        output_texture_internalformat = GL_RGB8;
        output_texture_format = GL_RGB;
        output_texture_data_type = GL_UNSIGNED_BYTE;
#endif
        pack_unpack_allignment = 1;
    } else if (elem_type == 1) {
        // 如果T是uint16_t时执行的代码

#ifdef GL_ES_VERSION_3_0

        input_texture_internalformat = GL_RGB16UI;
        input_texture_format = GL_RGB_INTEGER;
        input_texture_data_type = GL_UNSIGNED_SHORT;

        output_texture_internalformat = GL_RGBA16UI;
        output_texture_format = GL_RGBA_INTEGER;
        output_texture_data_type = GL_UNSIGNED_SHORT;

#else
        input_texture_internalformat = GL_RGB16;
        input_texture_format = GL_RGB;
        input_texture_data_type = GL_UNSIGNED_SHORT;

        output_texture_internalformat = GL_RGB16;
        output_texture_format = GL_RGB;
        output_texture_data_type = GL_UNSIGNED_SHORT;
#endif
        pack_unpack_allignment = 2;
    } else // elem_type == 2
    {
        // 如果T是float时执行的代码

#ifdef GL_ES_VERSION_3_0
        //    input_texture_internalformat = GL_RGB32F;
        //      input_texture_format =  GL_RGB;
        //    input_texture_data_type = GL_FLOAT;

        //      output_texture_internalformat = GL_RGBA32F;
        //      output_texture_format =  GL_RGBA;
        //      output_texture_data_type = GL_FLOAT;

        input_texture_internalformat = GL_RGB16F;
        input_texture_format = GL_RGB;
        input_texture_data_type = GL_FLOAT;

        output_texture_internalformat = GL_RGBA16F;
        output_texture_format = GL_RGBA;
        output_texture_data_type = GL_FLOAT;

#else
        input_texture_internalformat = GL_RGB32F;
        input_texture_format = GL_RGB;
        input_texture_data_type = GL_FLOAT;

        output_texture_internalformat = GL_RGB32F;
        output_texture_format = GL_RGB;
        output_texture_data_type = GL_FLOAT;
#endif
        pack_unpack_allignment = 4;
    }
    //    // 获取并打印 OpenGL ES 版本
    //    const char* version = (const char*)glGetString(GL_VERSION);
    //    if (version) {
    //        std::cout << "OpenGL ES Version: " << version << std::endl;
    //    } else {
    //        std::cerr << "Unable to get OpenGL ES version." << std::endl;
    //    }

    int dim = int(cbrt(luts_shape[2] / 3.0) + 0.5); // 3*dims^3 = w
    float binsize = 1.0001f / (dim - 1);
    auto lut_m_offset = luts_shape[2];

    auto M = maps_shape[1];
    auto map_h = maps_shape[2];
    auto map_w = maps_shape[3];
    auto map_channel_offset = map_h * map_w;

    GLuint outputTexture = 0, fbo = 0, inputTexture = 0;
    GLuint lutTextures = 0;
    GLuint mapTextures = 0;

    // shader program
    if (shaderProgram == -1) {
        std::cout << "@@[bro_l] trying opengl  ... " << std::endl;
        auto bok = createShaderProgram(shaderProgram, M, dim, elem_type);
        if (bok == false) {
            std::cout << "@@[bro_l] Fall back to cpu ..." << std::endl;
            return false;
        }
    }

    // 输出纹理
    outputTexture = createTexture2D(nullptr, width, height, output_texture_internalformat, output_texture_format, output_texture_data_type);
    //    checkGLError("outputTexture");

    auto ret = createFramebuffer(fbo, outputTexture, width, height);
    if (!ret) {
        glDeleteTextures(1, &outputTexture);
        return false;
    }

    glPixelStorei(GL_UNPACK_ALIGNMENT, pack_unpack_allignment);
#ifdef GL_ES_VERSION_3_0
    bool b_input_tex_nearest = true;
#else
    bool b_input_tex_nearest = false;
#endif

    inputTexture = createTexture2D(psrc_data, width, height, input_texture_internalformat, input_texture_format, input_texture_data_type, b_input_tex_nearest);

    {
#ifdef GL_ES_VERSION_3_0
        GLenum lut_internalFormat = GL_RGB16F;
        GLenum map_internalFormat = GL_R16F;
#else
        GLenum lut_internalFormat = GL_RGB32F;
        GLenum map_internalFormat = GL_R32F;
#endif

        glPixelStorei(GL_UNPACK_ALIGNMENT, 4);
        lutTextures = createTexture2DArray((const void *)luts, M * dim, dim, dim, lut_internalFormat, GL_RGB, GL_FLOAT);

        // glPixelStorei(GL_UNPACK_ALIGNMENT, 4);
        mapTextures = createTexture2DArray((const void *)maps, M, map_w, map_h, map_internalFormat, GL_RED, GL_FLOAT);
    }

    // 检查 OpenGL 错误
    //  checkGLError();

    // 渲染到FBO
    renderToFBO(fbo, GLuint(shaderProgram), inputTexture, lutTextures, mapTextures, M, width, height);

    // 保存输出纹理
    glBindFramebuffer(GL_FRAMEBUFFER, fbo);
    glReadBuffer(GL_COLOR_ATTACHMENT0);
    glPixelStorei(GL_PACK_ALIGNMENT, pack_unpack_allignment);
#ifdef GL_ES_VERSION_3_0
    //        GLint readFormat, readType;
    //        glGetIntegerv(GL_IMPLEMENTATION_COLOR_READ_FORMAT, &readFormat);
    //        glGetIntegerv(GL_IMPLEMENTATION_COLOR_READ_TYPE, &readType);
    //
    //        std::cout<<"Implementation color read format: "<< readFormat<<std::endl;
    //        std::cout<<"Implementation color read type: "<< readType<<std::endl;
    if (elem_type == 0) {
        cv::Mat rgba_result_mat(height, width, CV_8UC4);
        glReadPixels(0, 0, width, height, output_texture_format, output_texture_data_type, rgba_result_mat.data);

        cv::Mat rgb_result_mat(height, width, CV_8UC3, (void *)pdst_data);
        cv::cvtColor(rgba_result_mat, rgb_result_mat, cv::COLOR_RGBA2RGB);
    } else if (elem_type == 1) {
        cv::Mat rgba_result_mat(height, width, CV_16UC4);
        glReadPixels(0, 0, width, height, output_texture_format, output_texture_data_type, rgba_result_mat.data);

        cv::Mat rgb_result_mat(height, width, CV_16UC3, (void *)pdst_data);
        cv::cvtColor(rgba_result_mat, rgb_result_mat, cv::COLOR_RGBA2RGB);
    } else {
        cv::Mat rgba_result_mat(height, width, CV_32FC4);
        glReadPixels(0, 0, width, height, output_texture_format, output_texture_data_type, rgba_result_mat.data);

        cv::Mat rgb_result_mat(height, width, CV_32FC3, (void *)pdst_data);
        cv::cvtColor(rgba_result_mat, rgb_result_mat, cv::COLOR_RGBA2RGB);
    }
#else
    glReadPixels(0, 0, width, height, GL_RGB, output_texture_data_type, pdst_data);
#endif

    glBindFramebuffer(GL_FRAMEBUFFER, 0);

    // 清理资源
    glDeleteTextures(1, &inputTexture);
    glDeleteTextures(1, &lutTextures);
    glDeleteTextures(1, &mapTextures);

    glDeleteTextures(1, &outputTexture);
    glDeleteFramebuffers(1, &fbo);

    return true;
#else
    return false;
#endif
}

void release_propgram(int &shaderProgram) {
    if (shaderProgram >= 0) {
#if defined USE_OPENGL
        glDeleteProgram(GLuint(shaderProgram));
#endif
        shaderProgram = -1;
    }
}

} // namespace pgrawcvt
