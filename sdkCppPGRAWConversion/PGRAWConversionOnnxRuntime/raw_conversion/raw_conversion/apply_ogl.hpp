#ifndef APPLY_OGL_HPP_
#define APPLY_OGL_HPP_

#include <iostream>
#include <vector>

namespace pgrawcvt {

/**
 * @brief 使用OpenGL进行基于空间感知的三线性插值
 *
 * @param shader_type 着色器类型
 * @param shaderProgram 着色器程序
 * @param psrc_data 源数据指针
 * @param pdst_data 目标数据指针
 * @param width 图像宽度
 * @param height 图像高度
 * @param elem_type 元素类型
 * @param luts 查找表数据
 * @param luts_shape 查找表形状
 * @param maps 注意力图数据
 * @param maps_shape 注意力图形状
 * @return bool 操作是否成功
 */
bool triLinear_spatial_aware_opengl(int &shaderProgram, const void *psrc_data, void *pdst_data, int width, int height, int elem_type, const float *luts, std::vector<int64_t> &luts_shape, const float *maps, std::vector<int64_t> &maps_shape);

void release_propgram(int &shaderProgram);

} // namespace pgrawcvt

#endif // APPLY_OGL_HPP_
