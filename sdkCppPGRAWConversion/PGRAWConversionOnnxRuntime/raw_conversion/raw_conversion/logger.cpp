//
//  PGMotionTracker
//
//  Created by <PERSON><PERSON>ing<PERSON><PERSON> on 2019/7/10.
//  Copyright © 2019 ZhangJingQi. All rights reserved.
//

#include <chrono>
#include <cstdarg>
#include <cstdio>
#include <iostream>

#ifdef __ANDROID__
#include <android/log.h>
#include <jni.h>
#endif

#include "logger.hpp"

using namespace std::chrono;

namespace pgrawcvt {

Logger::Logger() {
    std::string value;
    char *val = getenv("PGRAWCVT_CPP_MIN_VLOG_LEVEL");
    if (val != nullptr) {
        value = std::string(val);
        if (value[0] != '0') {
            enable_log_ = true;
        }
    }

    val = getenv("PGRAWCVT_CPP_DEBUG_DIR");
    if (val != nullptr) {
        debug_dir_ = std::string(val);
        if (debug_dir_[0] != '0') {
            enable_debug_ = true;
        }
    }
}

Logger *Logger::SingletonLogger() {
    static Logger instance;
    return &instance;
}

std::int64_t Logger::now_micros() {
    auto now_ms = time_point_cast<milliseconds>(high_resolution_clock::now());
    return now_ms.time_since_epoch().count();
}

void Logger::latency_start() {
    if (enable_log_) {
        timer_stack_.push(now_micros());
    }
}

void Logger::latency_log(const char *fmt, ...) {
    if (enable_log_) {
        char space[1024] = {0};
        for (int i = 0; i < timer_stack_.size(); ++i) {
            space[i] = '-';
        }
        std::int64_t start_ts = timer_stack_.top();
        std::int64_t current_ts = now_micros();
        auto ts_distance = static_cast<float>(current_ts - start_ts);

        char info_str[2048] = {0};
        va_list args;
        va_start(args, fmt);
        vsnprintf(info_str, sizeof(info_str), fmt, args);
        va_end(args);

        if (log_print_) {
            log_stats("%s> %s: %.2f ms\n", space, info_str, ts_distance);
        } else {
            fprintf(stdout, "%s> %s: %.2f ms\n", space, info_str, ts_distance);

#ifdef __ANDROID__
            __android_log_print(ANDROID_LOG_INFO, "pgrawcvt::latency_log", "%s> %s: %.2f ms\n", space, info_str, ts_distance);
#endif
        }

        timer_stack_.pop();
    }
}

void Logger::log_info(const char *fmt, ...) {
    if (enable_log_) {
        char info_str[2048] = {0};
        va_list args;
        va_start(args, fmt);
        vsnprintf(info_str, sizeof(info_str), fmt, args);
        va_end(args);

        strncat(info_str, "\n", sizeof(info_str) - strlen(info_str) - 1);
        if (log_print_) {
            log_print_(info_str);
        } else {
            std::cout << info_str;
        }

#ifdef __ANDROID__
        __android_log_print(ANDROID_LOG_INFO, "pgrawcvt::log", "%s", info_str);
#endif
    }
}

void Logger::log_stats(const char *fmt, ...) {
    if (enable_log_) {
        char info_str[2048] = {0};
        va_list args;
        va_start(args, fmt);
        vsnprintf(info_str, sizeof(info_str), fmt, args);
        va_end(args);

        if (log_print_) {
            log_print_(info_str);
        } else {
            std::cout << info_str;
        }
    }
}

void Logger::set_log_print_func(LogCallback log_print) { log_print_ = log_print; }

std::string Logger::path_append(const std::string &p1, const std::string &p2) {
    char separator = '/';
    std::string tmp = p1;
#ifdef _WIN32
    separator = '\\';
#endif
    if (p1[p1.length() - 1] != separator) {
        tmp += separator;
        return (tmp + p2);
    } else {
        return (p1 + p2);
    }
}

void Logger::write_ppm(const char *name, const std::uint8_t *buffer, const std::uint32_t width, const std::uint32_t height, const std::uint32_t channel) {
    auto fp = fopen(name, "wb");
    if (fp) {
        fprintf(fp, "P6\n%d\n%d\n%d\n", width, height, 255);
        fwrite(buffer, width * height * channel, 1, fp);
        fclose(fp);
    }
}

void Logger::write_pgm(const char *name, const std::uint8_t *buffer, const std::uint32_t width, const std::uint32_t height, const std::uint32_t channel) {
    auto fp = fopen(name, "wb");
    if (fp) {
        fprintf(fp, "P5\n%d\n%d\n%d\n", width, height, 255);
        fwrite(buffer, width * height * channel, 1, fp);
        fclose(fp);
    }
}

void Logger::save_image(const char *name, const std::uint8_t *buffer, const std::uint32_t width, const std::uint32_t height, const std::uint32_t channel) {
    if (enable_debug_) {
        char full_name[1024] = {'\0'};
        snprintf(full_name, sizeof(full_name), "%02d-debug-%s", debug_idx_++, name);
        auto save_path = path_append(debug_dir_, full_name);
        if (channel == 1) {
            write_pgm(save_path.c_str(), buffer, width, height, channel);
        } else if (channel == 3) {
            write_ppm(save_path.c_str(), buffer, width, height, channel);
        }
    }
}

void Logger::save_2flow(const char *name, const std::uint8_t *buffer, const std::uint32_t width, const std::uint32_t height) {
    if (enable_debug_) {
        // 注释掉OpenCV相关代码，避免编译错误
        // 如果需要此功能，请确保项目中正确配置了OpenCV
        /*
        char full_name[1024] = {'\0'};
        snprintf(full_name, sizeof(full_name), "%02d-debug-%s", debug_idx_++, name);
        auto save_path = path_append(debug_dir_, full_name);
        cv::Mat flow(height, width, CV_32FC2, (void *)buffer);
        cv::Mat flow_parts[2];
        cv::split(flow, flow_parts);
        cv::Mat magnitude, angle, magn_norm;
        cv::cartToPolar(flow_parts[0], flow_parts[1], magnitude, angle, true);
        cv::normalize(magnitude, magn_norm, 0.0f, 1.0f, cv::NORM_MINMAX);
        angle *= ((1.f / 360.f) * (180.f / 255.f));
        cv::Mat _hsv[3], hsv, hsv8, rgb;
        _hsv[0] = angle;
        _hsv[1] = cv::Mat::ones(angle.size(), CV_32F);
        _hsv[2] = magn_norm;
        cv::merge(_hsv, 3, hsv);
        hsv.convertTo(hsv8, CV_8U, 255.0);
        cv::cvtColor(hsv8, rgb, cv::COLOR_HSV2RGB);
        std::uint8_t *flow_buffer = rgb.data;
        write_ppm(save_path.c_str(), flow_buffer, width, height, 3);
        */
    }
}

std::string Logger::get_debug_save_path(const char *name) {
    std::string save_path;
    if (enable_debug_) {
        char full_name[1024] = {'\0'};
        snprintf(full_name, sizeof(full_name), "%02d-debug-%s", debug_idx_++, name);
        save_path = path_append(debug_dir_, full_name);
    }
    return save_path;
}

void Logger::save_tensor(const char *name, const float *buffer, std::uint32_t width, std::uint32_t height, std::uint32_t channel, const std::vector<float> &mean, const std::vector<float> &norm) {
    if (enable_debug_) {
        char full_name[1024] = {'\0'};
        snprintf(full_name, sizeof(full_name), "%02d-debug-%s", debug_idx_++, name);
        auto save_path = path_append(debug_dir_, full_name);

        auto pixels_size = width * height * channel;
        auto pixels_buffer = std::shared_ptr<std::uint8_t>(new std::uint8_t[pixels_size], std::default_delete<std::uint8_t[]>());

        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                for (int c = 0; c < channel; ++c) {
                    // 添加实际的像素处理逻辑
                    int idx = (y * width + x) * channel + c;
                    float pixel_value = buffer[idx];
                    if (c < mean.size() && c < norm.size()) {
                        pixel_value = (pixel_value * norm[c]) + mean[c];
                    }
                    pixels_buffer.get()[idx] = static_cast<std::uint8_t>(std::max(0.0f, std::min(255.0f, pixel_value)));
                }
            }
        }

        if (channel == 1) {
            write_pgm(save_path.c_str(), pixels_buffer.get(), width, height, channel);
        } else if (channel == 3) {
            write_ppm(save_path.c_str(), pixels_buffer.get(), width, height, channel);
        }
    }
}

bool Logger::is_enabled_debug() { return enable_debug_; }

bool Logger::is_enabled_log() { return enable_log_; }

} // namespace pgrawcvt
