//
//  PGRAWConversion
//
//  Created by Tanqy on 2024/05/30.
//  Copyright © 2024 Tanqy. All rights reserved.
//

#include <algorithm>  // 引入算法库
#include <functional> // 引入函数对象库
#include <map>        // 引入map容器
#include <numeric>    // 引入数值算法库
#include <omp.h>      // 引入OpenMP库，用于多线程并行计算
#include <string>     // 引入字符串操作库
#include <thread>     // 引入线程管理库

#include "authority_manager.hpp"
#include "function_type.hpp"

#include "raw_conversion/raw_conversion/apply_ogl.hpp"
#include "raw_conversion/raw_conversion/logger.hpp"
#include "raw_conversion/raw_conversion/raw_conversion.hpp"
#include "raw_conversion/raw_conversion/rawcvt_lut_custom_std.hpp"
#include "raw_conversion/raw_conversion/rawcvt_lut_custom_sty.hpp"
#include "raw_conversion/raw_conversion/rawcvt_lut_step_1.hpp"
#include "raw_conversion/raw_conversion/rawcvt_lut_step_2_cls.hpp"
#include "raw_conversion/raw_conversion/rawcvt_lut_step_2_std.hpp"
#include "raw_conversion/raw_conversion/rawcvt_lut_step_2_sty.hpp"
#include "raw_conversion/raw_conversion/rawcvt_simulate_denoise.hpp"

// add PGGC
#include "raw_conversion/raw_conversion/general_computing.hpp"

namespace pgrawcvt { // 命名空间pgrawcvt，封装相关类和函数

// RAWConversion的内部实现类
class RAWConversion::RAWConversionInner {
  public:
    // RAWConversionInner() = default;
    RAWConversionInner() : logger_(Logger::SingletonLogger()) {} // 默认构造函数

    // 初始化函数
    bool init(const OrtConfig *ort_config, const std::string &key, const std::string &user_code, const std::string &prod_code, bool enable_opengl) {
        // 授权的初始化和校验
        bool ret = pgas::AuthorityManager::init(key, user_code, prod_code);
        bool available = pgas::AuthorityManager::verify(pgas::kFuncAIRAWConversion);
        if (!ret || !available) {
            pgas::AuthorizedError err = pgas::AuthorityManager::status();
            initialed_ = false;
            return false;
        }

        // 硬件配置
        ort_config_ = ort_config;
        enable_opengl_ = enable_opengl;

// 初始化OpenGL计算模块
#ifdef USE_OPENGL
        if (enable_opengl_) {
            if (!general_computing_.init()) {
                logger_->log_info("GeneralComputing init failed, fallback to CPU");
                enable_opengl_ = false;
            }
        }
#endif

        // 返回
        initialed_ = true;
        return true;
    }

    // 图像转档操作
    bool run(const RAWCVTConfig &config, const cv::Mat &src_image, cv::Mat &dst_image, std::uint32_t src_color_format, int &adjust_mode, bool adjust_custom) {
        if (!initialed_) { // 检查是否已经完成初始化
            return false;  // 如果未初始化，则返回false
        }

        // 风格分类
        adjust_mode = 0;

        // 模型配置
        enable_raw_lut = config.enable_raw_lut; // 使用 config 中的调亮配置
        enable_denoise = config.enable_denoise; // 使用 config 中的去噪配置
        enable_std_lut = config.enable_std_lut; // 使用 config 中的调标准色配置
        rawcvt_n_threads = config.n_threads;    // 使用 config 中的线程数量配置
        if (rawcvt_n_threads < 1) {             // 使用所有线程
            rawcvt_n_threads = num_cpu_threads_;
        }
        // rawcvt_auto_level = config.auto_level; // 使用 config 中的自动色阶配置

        // 初始化Session - lut_step_1
        if (enable_raw_lut && !usable_raw_lut) {
            logger_->latency_start();
            auto _rcl_1_rawlut_model_data = reinterpret_cast<const std::uint8_t *>(rawcvt_lut_step_1_data);
            auto _rcl_1_rawlut_model_size = rawcvt_lut_step_1_size;
            initSession(_rcl_1_rawlut_ort_session_, _rcl_1_rawlut_inp_count_, _rcl_1_rawlut_inp_chs_, _rcl_1_rawlut_inp_names_, _rcl_1_rawlut_inp_types_, _rcl_1_rawlut_inp_names_ptrs_, _rcl_1_rawlut_out_count_, _rcl_1_rawlut_out_chs_,
                        _rcl_1_rawlut_out_names_, _rcl_1_rawlut_out_types_, _rcl_1_rawlut_out_names_ptrs_, ort_config_->session_opts, *ort_config_->env, _rcl_1_rawlut_model_data, _rcl_1_rawlut_model_size);
            logger_->latency_log("PGRAWCVT: RAWCVT RAW session init successful");
            usable_raw_lut = true;
        }

        // 初始化Session - denoise
        if (enable_denoise && !usable_denoise) {
            logger_->latency_start();
            auto _rc_d_model_data = reinterpret_cast<const std::uint8_t *>(rawcvt_simulate_denoise_data);
            auto _rc_d_model_size = rawcvt_simulate_denoise_size;
            initSession(_rc_d_ort_session_, _rc_d_inp_count_, _rc_d_inp_chs_, _rc_d_inp_names_, _rc_d_inp_types_, _rc_d_inp_names_ptrs_, _rc_d_out_count_, _rc_d_out_chs_, _rc_d_out_names_, _rc_d_out_types_, _rc_d_out_names_ptrs_,
                        ort_config_->session_opts, *ort_config_->env, _rc_d_model_data, _rc_d_model_size);
            logger_->latency_log("PGRAWCVT: RAWCVT DAE session init successful");
            usable_denoise = true;
        }

        // 初始化Session - lut_step_2_std
        if (adjust_custom != stdlut_adjust_custom || rawcvt_adjust_type != config.adjustType) {
            usable_std_lut = false;                 // 意味着需要重新进行调色模型的初始化
            stdlut_adjust_custom = adjust_custom;   // 将stdlut_adjust_custom重新赋值与adjust_custom一致
            rawcvt_adjust_type = config.adjustType; // 基于 config 中的调色模式进行变更
        }
        if (enable_std_lut && !usable_std_lut) {
            // 分类模型
            if (rawcvt_adjust_type == RAWCVTConfig::AUTO) {
                logger_->latency_start();

                auto _rcl_2_lutcls_model_data = reinterpret_cast<const std::uint8_t *>(rawcvt_lut_step_2_cls_data);
                auto _rcl_2_lutcls_model_size = rawcvt_lut_step_2_cls_size;
                if (adjust_custom) {
                } // 置换

                initSession(_rcl_2_lutcls_ort_session_, _rcl_2_lutcls_inp_count_, _rcl_2_lutcls_inp_chs_, _rcl_2_lutcls_inp_names_, _rcl_2_lutcls_inp_types_, _rcl_2_lutcls_inp_names_ptrs_, _rcl_2_lutcls_out_count_, _rcl_2_lutcls_out_chs_,
                            _rcl_2_lutcls_out_names_, _rcl_2_lutcls_out_types_, _rcl_2_lutcls_out_names_ptrs_, ort_config_->session_opts, *ort_config_->env, _rcl_2_lutcls_model_data, _rcl_2_lutcls_model_size);
                logger_->latency_log("PGRAWCVT: RAWCVT CLS session init successful");
            }

            // 调色模型
            if (rawcvt_adjust_type == RAWCVTConfig::AUTO || rawcvt_adjust_type == RAWCVTConfig::NORMAL) {
                logger_->latency_start();

                auto _rcl_2_stdlut_model_data = reinterpret_cast<const std::uint8_t *>(rawcvt_lut_step_2_std_data);
                auto _rcl_2_stdlut_model_size = rawcvt_lut_step_2_std_size;
                if (adjust_custom) {
                    _rcl_2_stdlut_model_data = reinterpret_cast<const std::uint8_t *>(rawcvt_lut_custom_std_data);
                    _rcl_2_stdlut_model_size = rawcvt_lut_custom_std_size;
                } // 置换

                initSession(_rcl_2_stdlut_ort_session_, _rcl_2_stdlut_inp_count_, _rcl_2_stdlut_inp_chs_, _rcl_2_stdlut_inp_names_, _rcl_2_stdlut_inp_types_, _rcl_2_stdlut_inp_names_ptrs_, _rcl_2_stdlut_out_count_, _rcl_2_stdlut_out_chs_,
                            _rcl_2_stdlut_out_names_, _rcl_2_stdlut_out_types_, _rcl_2_stdlut_out_names_ptrs_, ort_config_->session_opts, *ort_config_->env, _rcl_2_stdlut_model_data, _rcl_2_stdlut_model_size);
                logger_->latency_log("PGRAWCVT: RAWCVT STD session init successful");
            }
            if (rawcvt_adjust_type == RAWCVTConfig::AUTO || rawcvt_adjust_type == RAWCVTConfig::STYLE) {
                logger_->latency_start();

                auto _rcl_2_stylut_model_data = reinterpret_cast<const std::uint8_t *>(rawcvt_lut_step_2_sty_data);
                auto _rcl_2_stylut_model_size = rawcvt_lut_step_2_sty_size;
                if (adjust_custom) {
                    _rcl_2_stylut_model_data = reinterpret_cast<const std::uint8_t *>(rawcvt_lut_custom_sty_data);
                    _rcl_2_stylut_model_size = rawcvt_lut_custom_sty_size;
                } // 置换

                initSession(_rcl_2_stylut_ort_session_, _rcl_2_stylut_inp_count_, _rcl_2_stylut_inp_chs_, _rcl_2_stylut_inp_names_, _rcl_2_stylut_inp_types_, _rcl_2_stylut_inp_names_ptrs_, _rcl_2_stylut_out_count_, _rcl_2_stylut_out_chs_,
                            _rcl_2_stylut_out_names_, _rcl_2_stylut_out_types_, _rcl_2_stylut_out_names_ptrs_, ort_config_->session_opts, *ort_config_->env, _rcl_2_stylut_model_data, _rcl_2_stylut_model_size);
                logger_->latency_log("PGRAWCVT: RAWCVT STY session init successful");
            }

            // TODO: 怎么保证切换模型时重新初始化
            usable_std_lut = true;
        }

        // 根据输入图像的位深选择处理路径
        if (src_image.depth() == CV_16U) {
            return runWithBitDepth<std::uint16_t>(config, src_image, dst_image, src_color_format, adjust_mode, CV_16U, 65535.0);
        } else if (src_image.depth() == CV_8U) {
            return runWithBitDepth<std::uint8_t>(config, src_image, dst_image, src_color_format, adjust_mode, CV_8U, 255.0);
        } else {
            std::cerr << "Unsupported image depth: " << src_image.depth() << std::endl;
            return false;
        }
    }

    // 模板化的处理函数，根据位深进行处理
    template <typename T> bool runWithBitDepth(const RAWCVTConfig &config, const cv::Mat &src_image, cv::Mat &dst_image, std::uint32_t src_color_format, int &adjust_mode, int cv_depth, double max_value) {
        // 颜色格式转换
        cv::Mat src_img_rgb;
        if (src_color_format == 1) { // 颜色模式: RGB -> RGB
            src_img_rgb = src_image;
        } else if (src_color_format == 2) { // BGR -> RGB
            cv::cvtColor(src_image, src_img_rgb, cv::COLOR_BGR2RGB);
        } else if (src_color_format == 4) { // RGBA -> RGB
            cv::cvtColor(src_image, src_img_rgb, cv::COLOR_RGBA2RGB);
        } else if (src_color_format == 5) { // BGRA -> RGB
            cv::cvtColor(src_image, src_img_rgb, cv::COLOR_BGRA2RGB);
        } else {
            fprintf(stderr, "Error: %d format is not supported.", src_color_format);
            return false;
        }

        // 准备3DLUT进行推理的数据: luts, attention_maps
        cv::Mat out_luts, out_maps;

        // 使用原始位深初始化输出图像
        cv::Mat out_img_rgb = src_img_rgb.clone();

        // 调色 - lut_step_1
        if (enable_raw_lut && usable_raw_lut) {
            logger_->latency_start();
            std::vector<float> inp_tensor_values;
            if (!inpRGB2Tensor(src_img_rgb, inp_tensor_values)) {
                // 处理错误情况
                return false; // 或其他适当的错误处理
            }
            infer3DLUT(out_img_rgb, out_luts, out_maps, _rcl_1_rawlut_ort_session_.get(), ort_config_->cpu_arena_mem.get(), _rcl_1_rawlut_inp_names_, _rcl_1_rawlut_inp_count_, _rcl_1_rawlut_out_names_, _rcl_1_rawlut_out_count_);
            if (!out_img_rgb.isContinuous()) {
                out_img_rgb = out_img_rgb.clone();
            }
            if (!out_luts.isContinuous()) {
                out_luts = out_luts.clone();
            }
            if (!out_maps.isContinuous()) {
                out_maps = out_maps.clone();
            }

            // 根据是否启用OpenGL选择处理方式
            bool ret = false;

#ifdef USE_OPENGL
            if (enable_opengl_) {
                logger_->log_info("Trying OpenGL acceleration...");

                // 重排LUT和maps数据为OpenGL所需格式
                float *luts_data = nullptr;
                float *maps_data = nullptr;
                std::vector<float> reordered_luts;
                std::vector<float> reordered_maps;
                std::vector<int64_t> luts_shape;
                std::vector<int64_t> maps_shape;

                // TODO: OpenGL 调用
                bool data_prepared = reorderLUTsAndMapsForOpenGL(out_luts, out_maps, luts_data, maps_data, reordered_luts, reordered_maps, luts_shape, maps_shape);
                if (data_prepared) {
                    // 根据模板参数T确定elem_type：uint8_t为0，uint16_t为1
                    int elem_type = std::is_same<T, std::uint8_t>::value ? 0 : 1;
                    ret = general_computing_.run(out_img_rgb.ptr<unsigned char>(0), out_img_rgb.ptr<unsigned char>(0), out_img_rgb.cols, out_img_rgb.rows, elem_type, luts_data, luts_shape, maps_data, maps_shape);

                    if (ret) {
                        logger_->log_info("PGRAWCVT: OpenGL acceleration successful!!!!!!!!!!!!!!!!!!");
                    } else {
                        logger_->log_info("OpenGL acceleration failed, fallback to CPU");
                    }
                } else {
                    logger_->log_info("OpenGL data preparation failed, fallback to CPU");
                }
            } else {
                logger_->log_info("OpenGL disabled, using CPU");
            }
#else
            logger_->log_info("OpenGL not compiled, using CPU");
#endif

            // 如果OpenGL处理失败或未启用，使用CPU处理
            if (!ret) {
                ret = triLinear_spatial_aware_template<T>(out_img_rgb, out_img_rgb, out_luts, out_maps, rawcvt_n_threads, max_value, false);
                if (ret) {
                    logger_->log_info("CPU processing successful");
                }
            }

            // 所有处理方式都失败
            if (!ret) {
                return false;
            }

            logger_->latency_log("PGRAWCVT: RAWCVT RAW run successful");
        }

        // 去噪
        if (enable_denoise && usable_denoise) {
            logger_->latency_start();
            out_img_rgb = inferDenoise_template<T>(out_img_rgb, _rc_d_ort_session_.get(), ort_config_->cpu_arena_mem.get(), _rc_d_inp_names_, _rc_d_inp_count_, _rc_d_out_names_, _rc_d_out_count_, cv_depth, max_value);
            logger_->latency_log("PGRAWCVT: RAWCVT DAE run successful");
        }

        // 调色 - lut_step_2
        if (enable_std_lut && usable_std_lut) {
            auto adjust_type = rawcvt_adjust_type;
            // 推理 LUT
            logger_->latency_start();
            if (adjust_type == RAWCVTConfig::AUTO) {
                auto prediction = inferCLS(out_img_rgb, _rcl_2_lutcls_ort_session_.get(), ort_config_->cpu_arena_mem.get(), _rcl_2_lutcls_inp_names_, _rcl_2_lutcls_inp_count_, _rcl_2_lutcls_out_names_, _rcl_2_lutcls_out_count_);
                if (prediction == "0") {
                    adjust_type = RAWCVTConfig::NORMAL;
                    adjust_mode = 0;
                } else {
                    adjust_type = RAWCVTConfig::STYLE;
                    adjust_mode = 1;
                }
            }
            if (adjust_type == RAWCVTConfig::NORMAL) {
                adjust_mode = 0;
                infer3DLUT(out_img_rgb, out_luts, out_maps, _rcl_2_stdlut_ort_session_.get(), ort_config_->cpu_arena_mem.get(), _rcl_2_stdlut_inp_names_, _rcl_2_stdlut_inp_count_, _rcl_2_stdlut_out_names_, _rcl_2_stdlut_out_count_);
                logger_->latency_log("PGRAWCVT: RAWCVT STD run successful");
            } else {
                adjust_mode = 1;
                infer3DLUT(out_img_rgb, out_luts, out_maps, _rcl_2_stylut_ort_session_.get(), ort_config_->cpu_arena_mem.get(), _rcl_2_stylut_inp_names_, _rcl_2_stylut_inp_count_, _rcl_2_stylut_out_names_, _rcl_2_stylut_out_count_);
                logger_->latency_log("PGRAWCVT: RAWCVT STY run successful");
            }
            // 应用 LUT
            logger_->latency_start();
            if (!out_img_rgb.isContinuous()) {
                out_img_rgb = out_img_rgb.clone();
            }
            if (!out_luts.isContinuous()) {
                out_luts = out_luts.clone();
            }
            if (!out_maps.isContinuous()) {
                out_maps = out_maps.clone();
            }

            // 根据是否启用OpenGL选择处理方式
            bool ret = false;

#ifdef USE_OPENGL
            if (enable_opengl_) {
                logger_->log_info("Trying OpenGL acceleration...");

                // 重排LUT和maps数据为OpenGL所需格式
                float *luts_data = nullptr;
                float *maps_data = nullptr;
                std::vector<float> reordered_luts;
                std::vector<float> reordered_maps;
                std::vector<int64_t> luts_shape;
                std::vector<int64_t> maps_shape;

                // TODO: OpenGL 调用
                bool data_prepared = reorderLUTsAndMapsForOpenGL(out_luts, out_maps, luts_data, maps_data, reordered_luts, reordered_maps, luts_shape, maps_shape);
                if (data_prepared) {
                    // 根据模板参数T确定elem_type：uint8_t为0，uint16_t为1
                    int elem_type = std::is_same<T, std::uint8_t>::value ? 0 : 1;
                    ret = general_computing_.run(out_img_rgb.ptr<unsigned char>(0), out_img_rgb.ptr<unsigned char>(0), out_img_rgb.cols, out_img_rgb.rows, elem_type, luts_data, luts_shape, maps_data, maps_shape);

                    if (ret) {
                        logger_->log_info("PGRAWCVT: OpenGL acceleration successful!!!!!!!!!!!!!!!!!!");
                    } else {
                        logger_->log_info("OpenGL acceleration failed, fallback to CPU");
                    }
                } else {
                    logger_->log_info("OpenGL data preparation failed, fallback to CPU");
                }
            } else {
                logger_->log_info("OpenGL disabled, using CPU");
            }
#else
            logger_->log_info("OpenGL not compiled, using CPU");
#endif

            // 如果OpenGL处理失败或未启用，使用CPU处理
            if (!ret) {
                ret = triLinear_spatial_aware_template<T>(out_img_rgb, out_img_rgb, out_luts, out_maps, rawcvt_n_threads, max_value, false);
                if (ret) {
                    logger_->log_info("CPU processing successful");
                }
            }

            // 所有处理方式都失败
            if (!ret) {
                return false;
            }

            logger_->latency_log("PGRAWCVT: RAWCVT LUT run successful");
        }

        // 颜色格式转换回原始格式
        if (src_color_format == 1) { // ModeC: RGB -> RGB
            dst_image = out_img_rgb;
        } else if (src_color_format == 2) { // RGB -> BGR
            cv::cvtColor(out_img_rgb, dst_image, cv::COLOR_RGB2BGR);
        } else if (src_color_format == 4) { // RGB -> RGBA
            cv::cvtColor(out_img_rgb, dst_image, cv::COLOR_RGB2RGBA);
        } else if (src_color_format == 5) { // RGB -> BGRA
            cv::cvtColor(out_img_rgb, dst_image, cv::COLOR_RGB2BGRA);
        } else {
            fprintf(stderr, "Error: %d format is not supported.", src_color_format);
            return false;
        }

        // 返回成功标志
        return true;
    }

    // **************************************************************************************************************************
    // ********************************************************* Auxiliary Function *********************************************
    // **************************************************************************************************************************
    // 定义一个内联函数来剪辑图像的像素值
    inline void clipFloatImg(cv::Mat &img, double minVal = 0.0, double maxVal = 1.0) {
        cv::max(img, minVal, img); // 确保图像的像素值不小于 minVal
        cv::min(img, maxVal, img); // 确保图像的像素值不大于 maxVal
    }

    // 将RGB的Float数据转化为Tensor
    bool inpRGB2Tensor(const cv::Mat &inp_rgb_, std::vector<float> &inp_rgb_data) {
        // 清空输出向量
        inp_rgb_data.clear();

        // 将输入图像转换为Float类型
        cv::Mat inp_rgb_float;
        if (inp_rgb_.depth() == CV_32F) {
            inp_rgb_float = inp_rgb_;
        } else if (inp_rgb_.depth() == CV_16U) {
            inp_rgb_.convertTo(inp_rgb_float, CV_32F, 1.0 / 65535.0);
        } else if (inp_rgb_.depth() == CV_8U) {
            inp_rgb_.convertTo(inp_rgb_float, CV_32F, 1.0 / 255.0);
        } else {
            fprintf(stderr, "Error: inp_rgb_float depth is not CV_32F or CV_16U or CV_8U!!!");
            return false;
        }
        clipFloatImg(inp_rgb_float); // 确保用于推理的图像像素值在 [0, 1] 范围内!

        // 预分配足够的空间以提高效率
        inp_rgb_data.reserve(inp_rgb_float.rows * inp_rgb_float.cols * inp_rgb_float.channels());

        // 确保将数据重塑为一维向量
        for (int c = 0; c < inp_rgb_float.channels(); ++c) {
            for (int y = 0; y < inp_rgb_float.rows; ++y) {
                const float *row_ptr = inp_rgb_float.ptr<float>(y);
                for (int x = 0; x < inp_rgb_float.cols; ++x) {
                    inp_rgb_data.push_back(row_ptr[x * inp_rgb_float.channels() + c]);
                }
            }
        }
        return true;
    }

    // 初始化Session
    bool initSession(std::unique_ptr<Ort::Session> &session, std::size_t &inp_count, std::vector<std::int32_t> &inp_chs, std::vector<const char *> &inp_names, std::vector<ONNXTensorElementDataType> &inp_types,
                     std::vector<Ort::AllocatedStringPtr> &inp_names_ptrs, std::size_t &out_count, std::vector<std::int32_t> &out_chs, std::vector<const char *> &out_names, std::vector<ONNXTensorElementDataType> &out_types,
                     std::vector<Ort::AllocatedStringPtr> &out_names_ptrs, const Ort::SessionOptions &session_opts, const Ort::Env &env, const std::uint8_t *model_data, std::size_t model_size) {
        // 创建一个 ONNX 会话对象
        session = std::unique_ptr<Ort::Session>(new Ort::Session(env, model_data, model_size, session_opts));

        // 获取 ONNX 模型中输入张量的数量
        inp_count = session->GetInputCount();
        for (std::size_t i = 0; i < inp_count; ++i) {
            // 收集输入张量的信息
            auto name_ptr = session->GetInputNameAllocated(i, ort_config_->default_allocator);
            inp_names.emplace_back(name_ptr.get());
            inp_names_ptrs.emplace_back(std::move(name_ptr));
            Ort::TypeInfo inputTypeInfo = session->GetInputTypeInfo(i);
            auto inputTensorInfo = inputTypeInfo.GetTensorTypeAndShapeInfo();
            inp_types.emplace_back(inputTensorInfo.GetElementType());
            inp_chs.emplace_back(static_cast<std::int32_t>(inputTensorInfo.GetShape()[1]));
        }

        // 获取 ONNX 模型中输出张量的数量
        out_count = session->GetOutputCount();
        for (std::size_t i = 0; i < out_count; ++i) {
            // 收集输出张量的信息
            auto name_ptr = session->GetOutputNameAllocated(i, ort_config_->default_allocator);
            out_names.emplace_back(name_ptr.get());
            out_names_ptrs.emplace_back(std::move(name_ptr));
            Ort::TypeInfo outputTypeInfo = session->GetOutputTypeInfo(i);
            auto outputTensorInfo = outputTypeInfo.GetTensorTypeAndShapeInfo();
            out_types.emplace_back(outputTensorInfo.GetElementType());
            out_chs.emplace_back(static_cast<std::int32_t>(outputTensorInfo.GetShape()[1]));
        }

        return true; // 返回成功标志
    }

    // inferCLS函数: 使用分类器以选择不同的调标准色模型。
    std::string inferCLS(const cv::Mat &img_rgb_16U_, Ort::Session *model_ort_session, const Ort::MemoryInfo *cpu_arena_mem, const std::vector<const char *> &input_names, const std::size_t input_count, const std::vector<const char *> &output_names,
                         const std::size_t output_count, const int infer_W = 224, const int infer_H = 224) {
        // 对源图像进行尺寸调整以匹配模型输入
        cv::Mat inp_img_rgb_resized;
        cv::resize(img_rgb_16U_, inp_img_rgb_resized, cv::Size(infer_W, infer_H));
        std::vector<float> inp_tensor_values;
        if (!inpRGB2Tensor(inp_img_rgb_resized, inp_tensor_values)) {
            // 处理错误情况
            return std::to_string(0); // 或其他适当的错误处理
        }

        // 准备推理输入数据
        std::vector<std::vector<std::int64_t>> inp_dims = {{1, 3, infer_H, infer_W}};
        auto inp_dim_data = inp_dims[0].data();                                                                                                           // 获取输入张量的形状数据
        auto inp_dim_size = inp_dims[0].size();                                                                                                           // 获取输入张量的维度数量
        auto inp_tensor_size = inp_tensor_values.size();                                                                                                  // 获取输入张量的数据总量
        auto inp_tensor_data = inp_tensor_values.data();                                                                                                  // 获取输入张量的数据指针
        std::vector<Ort::Value> inp_tensors;                                                                                                              // 创建输入张量的容器
        inp_tensors.emplace_back(Ort::Value::CreateTensor<float>(*cpu_arena_mem, inp_tensor_values.data(), inp_tensor_size, inp_dim_data, inp_dim_size)); // 生成输入张量

        // 执行模型推理
        auto out_tensors = model_ort_session->Run(Ort::RunOptions{nullptr}, input_names.data(), inp_tensors.data(), input_count, output_names.data(), output_count); // 获取模型输出
        if (out_tensors.empty()) {                                                                                                                                   // 检查输出张量是否为空
            return std::to_string(0);                                                                                                                                // 如果没有输出，返回false
        }

        // 解析分类结果
        std::vector<int64_t> cls_dim; // 存储输出张量的维度信息
        if (out_tensors[0].IsTensor()) {
            auto out_info_cls = out_tensors[0].GetTensorTypeAndShapeInfo();
            cls_dim.resize(out_info_cls.GetDimensionsCount());
            out_info_cls.GetDimensions(cls_dim.data(), cls_dim.size()); // 存储类别预测的维度信息
        }
        auto cls_data = out_tensors[0].GetTensorMutableData<float>(); // 获取类别预测数据指针

        // 寻找最大概率的类别
        int max_class = std::distance(cls_data, std::max_element(cls_data, cls_data + cls_dim[1]));
        std::string prediction = std::to_string(max_class); // 将预测结果转化为字符串

        // 返回预测结果
        return prediction;
    }

    // infer3DLUT函数：利用给定的ONNX模型对输入的RGB图像进行3D LUT推理，并输出相关的LUT和注意力图。
    bool infer3DLUT(const cv::Mat &inp_rgb_16U_, cv::Mat &out_luts, cv::Mat &out_maps, Ort::Session *model_ort_session, const Ort::MemoryInfo *cpu_arena_mem, const std::vector<const char *> &input_names, const std::size_t input_count,
                    const std::vector<const char *> &output_names, const std::size_t output_count, const int infer_W = 768, const int infer_H = 512) {
        // 对源图像进行尺寸调整以匹配模型输入
        cv::Mat inp_img_rgb_resized;
        cv::resize(inp_rgb_16U_, inp_img_rgb_resized, cv::Size(infer_W, infer_H));
        std::vector<float> inp_tensor_values;
        if (!inpRGB2Tensor(inp_img_rgb_resized, inp_tensor_values)) {
            // 处理错误情况
            return false; // 或其他适当的错误处理
        }

        // 准备推理输入数据
        std::vector<std::vector<std::int64_t>> inp_dims = {{1, 3, infer_H, infer_W}};
        auto inp_dim_data = inp_dims[0].data();                                                                                                           // 获取输入张量的形状数据
        auto inp_dim_size = inp_dims[0].size();                                                                                                           // 获取输入张量的维度数量
        auto inp_tensor_size = inp_tensor_values.size();                                                                                                  // 获取输入张量的数据总量
        auto inp_tensor_data = inp_tensor_values.data();                                                                                                  // 获取输入张量的数据指针
        std::vector<Ort::Value> inp_tensors;                                                                                                              // 创建输入张量的容器
        inp_tensors.emplace_back(Ort::Value::CreateTensor<float>(*cpu_arena_mem, inp_tensor_values.data(), inp_tensor_size, inp_dim_data, inp_dim_size)); // 生成输入张量

        // 展示推理输入数据
        // cv::Mat image_bgr;
        // cv::cvtColor(inp_img_rgb_resized, image_bgr, cv::COLOR_RGB2BGR);
        // image_bgr.convertTo(image_bgr, CV_8U, 1.0 / 255.0);
        // cv::imwrite("PGAI3DLut_inp_img.jpg", image_bgr);

        // 检查 session 是否为空
        // printf("session:%p tensor count:%d\n", model_ort_session, inp_tensors.size());
        // for (auto &input_name : input_names) {
        //     printf("input name:%s\n", input_name);
        // }
        // for (auto &output_name : output_names) {
        //     printf("output name:%s\n", output_name);
        // }
        // fflush(stdout);

        // 执行模型推理
        std::vector<int64_t> luts_dim, maps_dim;                                                                                                                     // 存储输出张量的维度信息
        auto out_tensors = model_ort_session->Run(Ort::RunOptions{nullptr}, input_names.data(), inp_tensors.data(), input_count, output_names.data(), output_count); // 获取模型输出
        if (out_tensors.empty()) {                                                                                                                                   // 检查输出张量是否为空
            return false;                                                                                                                                            // 如果没有输出，返回false
        }

        // 解析3DLUT结果
        if (out_tensors[0].IsTensor()) {
            auto out_info_luts = out_tensors[0].GetTensorTypeAndShapeInfo();
            for (auto i = 0; i < out_info_luts.GetShape().size(); i++) {
                luts_dim.push_back(static_cast<int>(out_info_luts.GetShape()[i])); // 存储LUT的维度信息
            }
        }
        auto luts_data = out_tensors[0].GetTensorMutableData<float>();                    // 获取LUT数据指针
        auto luts_c = luts_dim[1];                                                        // LUT的通道数
        auto luts_h = luts_dim[2];                                                        // LUT的高度
        auto luts_w = luts_dim[3];                                                        // LUT的宽度
        out_luts = cv::Mat(luts_h, luts_w, CV_32FC(luts_c), (void *)(luts_data)).clone(); // 创建LUT的cv::Mat格式数据

        // 解析注意力图结果
        if (out_tensors[1].IsTensor()) {
            auto out_info_maps = out_tensors[1].GetTensorTypeAndShapeInfo();
            for (auto i = 0; i < out_info_maps.GetShape().size(); i++) {
                maps_dim.push_back(static_cast<int>(out_info_maps.GetShape()[i])); // 存储注意力图的维度信息
            }
        }
        auto maps_data = out_tensors[1].GetTensorMutableData<float>();                  // 获取注意力图数据指针
        auto maps_c = maps_dim[1];                                                      // 注意力图的通道数
        auto maps_h = maps_dim[2];                                                      // 注意力图的高度
        auto maps_w = maps_dim[3];                                                      // 注意力图的宽度
        std::vector<cv::Mat> map_channels(maps_c);                                      // 准备多个注意力通道以取出注意力图
        for (int i = 0; i < maps_c; ++i) {                                              // 遍历每个通道
            map_channels[i] = cv::Mat(maps_h, maps_w, CV_32FC(1), (void *)(maps_data)); // 单张注意力图
            maps_data += maps_h * maps_w;                                               // 移动指针位置
        }
        out_maps = cv::Mat(maps_h, maps_w, CV_32FC(maps_c));
        cv::merge(map_channels, out_maps);

        // 展示注意力图结果
        // cv::split(out_maps, map_channels);
        // for (int i = 0; i < maps_c; ++i) { // 遍历每个通道，保存为 JPEG
        //     cv::Mat channel_8bit;          // 转换到 0-255 的范围
        //     map_channels[i].convertTo(channel_8bit, CV_8U, 255.0);
        //     std::string filename = "maps_" + std::to_string(i) + ".jpg"; // 构建文件名
        //     cv::imwrite(filename, channel_8bit);                         // 保存图像
        // }

        // 返回成功标志
        return true;
    }

    // 将LUT和maps数据重排为OpenGL所需格式的函数
    bool reorderLUTsAndMapsForOpenGL(const cv::Mat &out_luts, const cv::Mat &out_maps, float *&luts_data_out, float *&maps_data_out, std::vector<float> &reordered_luts, std::vector<float> &reordered_maps, std::vector<int64_t> &luts_shape,
                                     std::vector<int64_t> &maps_shape) {
        // 打印LUT和maps的形状
        // std::cout << "LUTs shape: [" << out_luts.rows << " x " << out_luts.cols << "], channels: " << out_luts.channels() << std::endl;
        // std::cout << "Maps shape: [" << out_maps.rows << " x " << out_maps.cols << "], channels: " << out_maps.channels() << std::endl;

        // 检查LUT形状是否符合预期 [8 x 14739], channels: 1
        if (out_luts.rows != 8 || out_luts.cols != 14739 || out_luts.channels() != 1) {
            std::cerr << "LUTs形状不符合预期，无法转换" << std::endl;
            return false;
        }

        // 检查maps形状是否符合预期 [256 x 256], channels: 8
        if (out_maps.rows != 256 || out_maps.cols != 256 || out_maps.channels() != 8) {
            std::cerr << "Maps形状不符合预期，无法转换" << std::endl;
            return false;
        }

        // 转换LUT形状从[8 x 14739], channels: 1到OpenGL所需的格式
        float *luts_data = (float *)out_luts.data;

        // 创建临时缓冲区用于重排LUT数据
        const int bins = 17;
        const int channels = 3;
        const int bins_cubed = bins * bins * bins;
        const int lut_size = bins_cubed * channels;
        reordered_luts.resize(8 * lut_size);

        // 对每个LUT进行内存布局调整
        for (int n = 0; n < 8; n++) {
            float *src_lut = luts_data + n * 14739;
            float *dst_lut = reordered_luts.data() + n * lut_size;

            // 假设源数据是按照 [3][bins^3] 排列
            // 我们需要转换为 [bins][bins][bins][3] 排列
            // 首先，将源数据解析为3个通道
            std::vector<float> r_channel(bins_cubed);
            std::vector<float> g_channel(bins_cubed);
            std::vector<float> b_channel(bins_cubed);

            for (int i = 0; i < bins_cubed; i++) {
                r_channel[i] = src_lut[i];
                g_channel[i] = src_lut[bins_cubed + i];
                b_channel[i] = src_lut[2 * bins_cubed + i];
            }

            // 然后，按照特定顺序重新排列数据
            for (int i = 0; i < bins; i++) {
                for (int j = 0; j < bins; j++) {
                    for (int k = 0; k < bins; k++) {
                        // 计算在bins^3中的线性索引
                        int linear_idx = i * bins * bins + j * bins + k;

                        // 计算在目标数组中的索引
                        int dst_idx = linear_idx * 3;

                        // 按照RGB顺序存储
                        dst_lut[dst_idx] = r_channel[linear_idx];
                        dst_lut[dst_idx + 1] = g_channel[linear_idx];
                        dst_lut[dst_idx + 2] = b_channel[linear_idx];
                    }
                }
            }
        }

        // 使用重排后的LUT数据
        luts_data_out = reordered_luts.data();

        // 转换maps形状从[256 x 256], channels: 8到OpenGL所需的格式
        float *maps_data = (float *)out_maps.data;

        // 创建临时缓冲区用于重排maps数据
        const int map_h = 256;
        const int map_w = 256;
        const int map_channels = 8;
        reordered_maps.resize(map_h * map_w * map_channels);

        // 重排maps数据
        // 假设源数据是按照[256][256][8]排列（HWC格式）
        // 我们需要转换为[8][256][256]排列（CHW格式）
        for (int c = 0; c < map_channels; c++) {
            for (int h = 0; h < map_h; h++) {
                for (int w = 0; w < map_w; w++) {
                    // 源索引：h * w * c + w * c + c
                    int src_idx = h * map_w * map_channels + w * map_channels + c;

                    // 目标索引：c * h * w + h * w + w
                    int dst_idx = c * map_h * map_w + h * map_w + w;

                    reordered_maps[dst_idx] = maps_data[src_idx];
                }
            }
        }

        // 使用重排后的maps数据
        maps_data_out = reordered_maps.data();

        // 准备形状数据
        luts_shape = {1, 8, 14739};
        maps_shape = {1, 8, 256, 256};

        return true;
    }

    // TODO: 模板化的基于空间感知的三线性插值函数
    template <typename T> bool triLinear_spatial_aware_template(cv::Mat &src, cv::Mat &dst, cv::Mat &luts, cv::Mat &maps, int nthreads, double max_value, bool accelerator = false) {
        // 获取LUT数据的指针
        const float *pluts_data = luts.ptr<float>(0);

        // 计算LUT的维度和binsize，用于后续计算
        int dim = int(std::cbrt(luts.cols / 3.0) + 0.5); // 计算每个维度的大小
        float binsize = 1.000000001f / (dim - 1);        // 计算每个bin的大小
        int M = maps.channels();                         // 获取maps的通道数

        unsigned int dim2 = dim * dim;
        unsigned int shift = dim2 * dim;  // 计算三维索引的偏移量
        unsigned int shift_2 = shift * 2; // 为G和B通道计算偏移量

        // 根据数据类型选择优化的处理路径
        if (std::is_same<T, std::uint8_t>::value) {
            return triLinear_spatial_aware_uint8(src, dst, luts, maps, nthreads, accelerator, pluts_data, dim, binsize, M, dim2, shift, shift_2);
        } else if (std::is_same<T, std::uint16_t>::value) {
            return triLinear_spatial_aware_uint16(src, dst, luts, maps, nthreads, accelerator, pluts_data, dim, binsize, M, dim2, shift, shift_2);
        } else {
            // 其他类型保持原有的浮点处理方式
            return triLinear_spatial_aware_float(src, dst, luts, maps, nthreads, max_value, accelerator, pluts_data, dim, binsize, M, dim2, shift, shift_2);
        }
    }

    // 8位图像优化处理函数
    bool triLinear_spatial_aware_uint8(cv::Mat &src, cv::Mat &dst, cv::Mat &luts, cv::Mat &maps, int nthreads, bool accelerator, const float *pluts_data, int dim, float binsize, int M, unsigned int dim2, unsigned int shift, unsigned int shift_2) {
        int c = src.channels();
        std::vector<cv::Mat> inp_channels(c);
        cv::Mat image_hp, image_inp;

        // 对于8位图像，我们可以直接在8位域进行大部分操作
        if (accelerator) {
            cv::resize(src, image_inp, cv::Size(src.cols / 2, src.rows / 2), 0, 0, cv::INTER_LINEAR);
            cv::resize(image_inp, image_hp, cv::Size(src.cols, src.rows), 0, 0, cv::INTER_LINEAR);
            cv::subtract(src, image_hp, image_hp); // 计算高频成分（注意：这里可能有符号问题，需要处理）
            cv::split(image_inp, inp_channels);
        } else {
            image_inp = src.clone();
            cv::split(image_inp, inp_channels);
        }

        std::vector<cv::Mat> out_channels(c);
        for (int i = 0; i < c; i++) {
            out_channels[i] = cv::Mat::zeros(inp_channels[i].size(), CV_8U);
        }

        // 准备注意力图通道
        std::vector<cv::Mat> map_channels(M);
        cv::split(maps, map_channels);

        // 预计算缩放因子，避免重复除法
        const float inv_255 = 1.0f / 255.0f;
        const float scale_to_255 = 255.0f;

#pragma omp parallel for num_threads(nthreads)
        for (int i = 0; i < image_inp.rows; i++) {
            for (int j = 0; j < image_inp.cols; j++) {
                // 读取8位RGB值并转换为[0,1]范围
                float r = inp_channels[0].at<uint8_t>(i, j) * inv_255;
                float g = inp_channels[1].at<uint8_t>(i, j) * inv_255;
                float b = inp_channels[2].at<uint8_t>(i, j) * inv_255;

                // 计算在maps中的对应位置（复用原逻辑）
                float i_in_map = float(i * (maps.rows - 1)) / (image_inp.rows - 1);
                int i_in_map_index = int(i_in_map);
                float alpha_i = i_in_map - i_in_map_index;
                if (i == image_inp.rows - 1) {
                    i_in_map_index = maps.rows - 2;
                    alpha_i = 1.f;
                }

                float j_in_map = float(j * (maps.cols - 1)) / (image_inp.cols - 1);
                int j_in_map_index = int(j_in_map);
                float alpha_j = j_in_map - j_in_map_index;
                if (j == image_inp.cols - 1) {
                    j_in_map_index = maps.cols - 2;
                    alpha_j = 1.f;
                }

                int cur_pos_map_offset = j_in_map_index + i_in_map_index * maps.cols;

                // LUT索引计算
                float r_scale = r / binsize;
                float g_scale = g / binsize;
                float b_scale = b / binsize;

                unsigned int r_id = (unsigned int)(r_scale);
                unsigned int g_id = (unsigned int)(g_scale);
                unsigned int b_id = (unsigned int)(b_scale);

                float r_d = r_scale - r_id;
                float g_d = g_scale - g_id;
                float b_d = b_scale - b_id;

                // 8个顶点索引
                unsigned int id000 = r_id + g_id * dim + b_id * dim2;
                unsigned int id100 = id000 + 1;
                unsigned int id010 = id000 + dim;
                unsigned int id001 = id000 + dim2;
                unsigned int id110 = id100 + dim;
                unsigned int id101 = id001 + 1;
                unsigned int id011 = id010 + dim2;
                unsigned int id111 = id011 + 1;

                float new_r = 0, new_g = 0, new_b = 0;

                // 对每个maps通道进行处理
                for (int mi = 0; mi < M; mi++) {
                    const float *plut = pluts_data + luts.cols * mi;

                    // 三线性插值（保持浮点精度）
                    // R通道
                    float xt0 = plut[id000] + r_d * (plut[id100] - plut[id000]);
                    float xt1 = plut[id010] + r_d * (plut[id110] - plut[id010]);
                    float yt0 = plut[id001] + r_d * (plut[id101] - plut[id001]);
                    float yt1 = plut[id011] + r_d * (plut[id111] - plut[id011]);
                    float zt0 = xt0 + g_d * (xt1 - xt0);
                    float zt1 = yt0 + g_d * (yt1 - yt0);
                    float tmp_r = zt0 + b_d * (zt1 - zt0);

                    // G通道
                    xt0 = plut[id000 + shift] + r_d * (plut[id100 + shift] - plut[id000 + shift]);
                    xt1 = plut[id010 + shift] + r_d * (plut[id110 + shift] - plut[id010 + shift]);
                    yt0 = plut[id001 + shift] + r_d * (plut[id101 + shift] - plut[id001 + shift]);
                    yt1 = plut[id011 + shift] + r_d * (plut[id111 + shift] - plut[id011 + shift]);
                    zt0 = xt0 + g_d * (xt1 - xt0);
                    zt1 = yt0 + g_d * (yt1 - yt0);
                    float tmp_g = zt0 + b_d * (zt1 - zt0);

                    // B通道
                    xt0 = plut[id000 + shift_2] + r_d * (plut[id100 + shift_2] - plut[id000 + shift_2]);
                    xt1 = plut[id010 + shift_2] + r_d * (plut[id110 + shift_2] - plut[id010 + shift_2]);
                    yt0 = plut[id001 + shift_2] + r_d * (plut[id101 + shift_2] - plut[id001 + shift_2]);
                    yt1 = plut[id011 + shift_2] + r_d * (plut[id111 + shift_2] - plut[id011 + shift_2]);
                    zt0 = xt0 + g_d * (xt1 - xt0);
                    zt1 = yt0 + g_d * (yt1 - yt0);
                    float tmp_b = zt0 + b_d * (zt1 - zt0);

                    // 权重计算
                    const float *pmap = map_channels[mi].ptr<float>();
                    pmap += cur_pos_map_offset;
                    float att_y0 = pmap[0] + (pmap[1] - pmap[0]) * alpha_j;
                    float att_y1 = pmap[map_channels[mi].cols] + (pmap[map_channels[mi].cols + 1] - pmap[map_channels[mi].cols]) * alpha_j;
                    float att = att_y0 + (att_y1 - att_y0) * alpha_i;

                    new_r += att * tmp_r;
                    new_g += att * tmp_g;
                    new_b += att * tmp_b;
                }

                // 直接转换为8位并存储，避免中间浮点图像
                out_channels[0].at<uint8_t>(i, j) = cv::saturate_cast<uint8_t>(new_r * scale_to_255);
                out_channels[1].at<uint8_t>(i, j) = cv::saturate_cast<uint8_t>(new_g * scale_to_255);
                out_channels[2].at<uint8_t>(i, j) = cv::saturate_cast<uint8_t>(new_b * scale_to_255);
            }
        }

        // 合并通道
        cv::Mat output_8bit;
        cv::merge(out_channels, output_8bit);

        if (accelerator) {
            cv::resize(output_8bit, output_8bit, cv::Size(src.cols, src.rows), 0, 0, cv::INTER_LINEAR);
            // 处理高频成分加回（需要小心符号问题）
            cv::Mat temp;
            output_8bit.convertTo(temp, CV_16S);
            image_hp.convertTo(image_hp, CV_16S);
            cv::add(temp, image_hp, temp);
            temp.convertTo(dst, CV_8U);
        } else {
            dst = output_8bit;
        }

        return true;
    }

    // 16位图像优化处理函数
    bool triLinear_spatial_aware_uint16(cv::Mat &src, cv::Mat &dst, cv::Mat &luts, cv::Mat &maps, int nthreads, bool accelerator, const float *pluts_data, int dim, float binsize, int M, unsigned int dim2, unsigned int shift, unsigned int shift_2) {
        int c = src.channels();
        std::vector<cv::Mat> inp_channels(c);
        cv::Mat image_hp, image_inp;

        if (accelerator) {
            cv::resize(src, image_inp, cv::Size(src.cols / 2, src.rows / 2), 0, 0, cv::INTER_LINEAR);
            cv::resize(image_inp, image_hp, cv::Size(src.cols, src.rows), 0, 0, cv::INTER_LINEAR);
            cv::subtract(src, image_hp, image_hp);
            cv::split(image_inp, inp_channels);
        } else {
            image_inp = src.clone();
            cv::split(image_inp, inp_channels);
        }

        std::vector<cv::Mat> out_channels(c);
        for (int i = 0; i < c; i++) {
            out_channels[i] = cv::Mat::zeros(inp_channels[i].size(), CV_16U);
        }

        std::vector<cv::Mat> map_channels(M);
        cv::split(maps, map_channels);

        const float inv_65535 = 1.0f / 65535.0f;
        const float scale_to_65535 = 65535.0f;

#pragma omp parallel for num_threads(nthreads)
        for (int i = 0; i < image_inp.rows; i++) {
            for (int j = 0; j < image_inp.cols; j++) {
                // 读取16位RGB值并转换为[0,1]范围
                float r = inp_channels[0].at<uint16_t>(i, j) * inv_65535;
                float g = inp_channels[1].at<uint16_t>(i, j) * inv_65535;
                float b = inp_channels[2].at<uint16_t>(i, j) * inv_65535;

                // maps位置计算（复用原逻辑）
                float i_in_map = float(i * (maps.rows - 1)) / (image_inp.rows - 1);
                int i_in_map_index = int(i_in_map);
                float alpha_i = i_in_map - i_in_map_index;
                if (i == image_inp.rows - 1) {
                    i_in_map_index = maps.rows - 2;
                    alpha_i = 1.f;
                }

                float j_in_map = float(j * (maps.cols - 1)) / (image_inp.cols - 1);
                int j_in_map_index = int(j_in_map);
                float alpha_j = j_in_map - j_in_map_index;
                if (j == image_inp.cols - 1) {
                    j_in_map_index = maps.cols - 2;
                    alpha_j = 1.f;
                }

                int cur_pos_map_offset = j_in_map_index + i_in_map_index * maps.cols;

                // LUT索引计算
                float r_scale = r / binsize;
                float g_scale = g / binsize;
                float b_scale = b / binsize;

                unsigned int r_id = (unsigned int)(r_scale);
                unsigned int g_id = (unsigned int)(g_scale);
                unsigned int b_id = (unsigned int)(b_scale);

                float r_d = r_scale - r_id;
                float g_d = g_scale - g_id;
                float b_d = b_scale - b_id;

                // 8个顶点索引
                unsigned int id000 = r_id + g_id * dim + b_id * dim2;
                unsigned int id100 = id000 + 1;
                unsigned int id010 = id000 + dim;
                unsigned int id001 = id000 + dim2;
                unsigned int id110 = id100 + dim;
                unsigned int id101 = id001 + 1;
                unsigned int id011 = id010 + dim2;
                unsigned int id111 = id011 + 1;

                float new_r = 0, new_g = 0, new_b = 0;

                // 三线性插值和权重计算（与8位版本相同的逻辑）
                for (int mi = 0; mi < M; mi++) {
                    const float *plut = pluts_data + luts.cols * mi;

                    // R通道三线性插值
                    float xt0 = plut[id000] + r_d * (plut[id100] - plut[id000]);
                    float xt1 = plut[id010] + r_d * (plut[id110] - plut[id010]);
                    float yt0 = plut[id001] + r_d * (plut[id101] - plut[id001]);
                    float yt1 = plut[id011] + r_d * (plut[id111] - plut[id011]);
                    float zt0 = xt0 + g_d * (xt1 - xt0);
                    float zt1 = yt0 + g_d * (yt1 - yt0);
                    float tmp_r = zt0 + b_d * (zt1 - zt0);

                    // G通道三线性插值
                    xt0 = plut[id000 + shift] + r_d * (plut[id100 + shift] - plut[id000 + shift]);
                    xt1 = plut[id010 + shift] + r_d * (plut[id110 + shift] - plut[id010 + shift]);
                    yt0 = plut[id001 + shift] + r_d * (plut[id101 + shift] - plut[id001 + shift]);
                    yt1 = plut[id011 + shift] + r_d * (plut[id111 + shift] - plut[id011 + shift]);
                    zt0 = xt0 + g_d * (xt1 - xt0);
                    zt1 = yt0 + g_d * (yt1 - yt0);
                    float tmp_g = zt0 + b_d * (zt1 - zt0);

                    // B通道三线性插值
                    xt0 = plut[id000 + shift_2] + r_d * (plut[id100 + shift_2] - plut[id000 + shift_2]);
                    xt1 = plut[id010 + shift_2] + r_d * (plut[id110 + shift_2] - plut[id010 + shift_2]);
                    yt0 = plut[id001 + shift_2] + r_d * (plut[id101 + shift_2] - plut[id001 + shift_2]);
                    yt1 = plut[id011 + shift_2] + r_d * (plut[id111 + shift_2] - plut[id011 + shift_2]);
                    zt0 = xt0 + g_d * (xt1 - xt0);
                    zt1 = yt0 + g_d * (yt1 - yt0);
                    float tmp_b = zt0 + b_d * (zt1 - zt0);

                    // 权重计算
                    const float *pmap = map_channels[mi].ptr<float>();
                    pmap += cur_pos_map_offset;
                    float att_y0 = pmap[0] + (pmap[1] - pmap[0]) * alpha_j;
                    float att_y1 = pmap[map_channels[mi].cols] + (pmap[map_channels[mi].cols + 1] - pmap[map_channels[mi].cols]) * alpha_j;
                    float att = att_y0 + (att_y1 - att_y0) * alpha_i;

                    new_r += att * tmp_r;
                    new_g += att * tmp_g;
                    new_b += att * tmp_b;
                }

                // 直接转换为16位并存储
                out_channels[0].at<uint16_t>(i, j) = cv::saturate_cast<uint16_t>(new_r * scale_to_65535);
                out_channels[1].at<uint16_t>(i, j) = cv::saturate_cast<uint16_t>(new_g * scale_to_65535);
                out_channels[2].at<uint16_t>(i, j) = cv::saturate_cast<uint16_t>(new_b * scale_to_65535);
            }
        }

        cv::Mat output_16bit;
        cv::merge(out_channels, output_16bit);

        if (accelerator) {
            cv::resize(output_16bit, output_16bit, cv::Size(src.cols, src.rows), 0, 0, cv::INTER_LINEAR);
            cv::add(image_hp, output_16bit, dst);
        } else {
            dst = output_16bit;
        }

        return true;
    }

    // 保留原有的浮点处理函数作为回退方案
    bool triLinear_spatial_aware_float(cv::Mat &src, cv::Mat &dst, cv::Mat &luts, cv::Mat &maps, int nthreads, double max_value, bool accelerator, const float *pluts_data, int dim, float binsize, int M, unsigned int dim2, unsigned int shift,
                                       unsigned int shift_2) {
        // 分割输入图像通道并准备输出的通道
        int c = src.channels();
        std::vector<cv::Mat> inp_channels(c); // 输入通道
        cv::Mat image_hp, image_inp;

        // 根据输入类型转换为CV_32F进行处理
        cv::Mat image_float;
        src.convertTo(image_float, CV_32F, 1.0 / max_value); // 将原始类型转换为[0,1]范围的浮点型

        if (accelerator) { // 利用小图进行加速
            cv::resize(image_float, image_inp, cv::Size(src.cols / 2, src.rows / 2), 0, 0, cv::INTER_LINEAR);
            cv::resize(image_inp, image_hp, cv::Size(src.cols, src.rows), 0, 0, cv::INTER_LINEAR);
            cv::subtract(image_float, image_hp, image_hp); // 计算原始图像与放大后的图像的差异，得到高频成分
            cv::split(image_inp, inp_channels);
        } else {
            image_inp = image_float.clone();
            cv::split(image_inp, inp_channels);
        }
        std::vector<cv::Mat> out_channels(c); // 输出通道
        for (int i = 0; i < c; i++) {
            out_channels[i] = inp_channels[i].clone();
        }

        // 准备输入注意力图中的八个单独通道
        std::vector<cv::Mat> map_channels(M);
        cv::split(maps, map_channels);

// 使用多线程处理每个像素
#pragma omp parallel for num_threads(nthreads)
        for (int i = 0; i < image_inp.rows; i++) {
            for (int j = 0; j < image_inp.cols; j++) {
                // 读取当前像素的RGB值
                float r = inp_channels[0].at<float>(i, j);
                float g = inp_channels[1].at<float>(i, j);
                float b = inp_channels[2].at<float>(i, j);

                // 计算当前像素在maps中的对应位置
                float i_in_map = float(i * (maps.rows - 1)) / (image_inp.rows - 1);
                int i_in_map_index = int(i_in_map);
                float alpha_i = i_in_map - i_in_map_index;
                if (i == image_inp.rows - 1) {
                    i_in_map_index = maps.rows - 2;
                    alpha_i = 1.f;
                }

                float j_in_map = float(j * (maps.cols - 1)) / (image_inp.cols - 1);
                int j_in_map_index = int(j_in_map);
                float alpha_j = j_in_map - j_in_map_index;
                if (j == image_inp.cols - 1) {
                    j_in_map_index = maps.cols - 2;
                    alpha_j = 1.f;
                }

                int cur_pos_map_offset = j_in_map_index + i_in_map_index * maps.cols;

                // 根据RGB值和binsize计算在LUT中的位置
                float r_scale = r / binsize;
                float g_scale = g / binsize;
                float b_scale = b / binsize;

                unsigned int r_id = (unsigned int)(r_scale);
                unsigned int g_id = (unsigned int)(g_scale);
                unsigned int b_id = (unsigned int)(b_scale);

                float r_d = r_scale - r_id;
                float g_d = g_scale - g_id;
                float b_d = b_scale - b_id;

                // 计算LUT中8个顶点的索引
                unsigned int id000 = r_id + g_id * dim + b_id * dim2;
                unsigned int id100 = id000 + 1;
                unsigned int id010 = id000 + dim;
                unsigned int id001 = id000 + dim2;
                unsigned int id110 = id100 + dim;
                unsigned int id101 = id001 + 1;
                unsigned int id011 = id010 + dim2;
                unsigned int id111 = id011 + 1;

                float new_r = 0, new_g = 0, new_b = 0;
                // 对每个maps通道进行处理
                for (int mi = 0; mi < M; mi++) {
                    const float *plut = pluts_data + luts.cols * mi;

                    // 对R通道进行三线性插值
                    float xt0 = plut[id000] + r_d * (plut[id100] - plut[id000]);
                    float xt1 = plut[id010] + r_d * (plut[id110] - plut[id010]);
                    float yt0 = plut[id001] + r_d * (plut[id101] - plut[id001]);
                    float yt1 = plut[id011] + r_d * (plut[id111] - plut[id011]);

                    float zt0 = xt0 + g_d * (xt1 - xt0);
                    float zt1 = yt0 + g_d * (yt1 - yt0);

                    float tmp_r = zt0 + b_d * (zt1 - zt0);

                    // 对G通道进行三线性插值
                    xt0 = plut[id000 + shift] + r_d * (plut[id100 + shift] - plut[id000 + shift]);
                    xt1 = plut[id010 + shift] + r_d * (plut[id110 + shift] - plut[id010 + shift]);
                    yt0 = plut[id001 + shift] + r_d * (plut[id101 + shift] - plut[id001 + shift]);
                    yt1 = plut[id011 + shift] + r_d * (plut[id111 + shift] - plut[id011 + shift]);

                    zt0 = xt0 + g_d * (xt1 - xt0);
                    zt1 = yt0 + g_d * (yt1 - yt0);

                    float tmp_g = zt0 + b_d * (zt1 - zt0);

                    // 对B通道进行三线性插值
                    xt0 = plut[id000 + shift_2] + r_d * (plut[id100 + shift_2] - plut[id000 + shift_2]);
                    xt1 = plut[id010 + shift_2] + r_d * (plut[id110 + shift_2] - plut[id010 + shift_2]);
                    yt0 = plut[id001 + shift_2] + r_d * (plut[id101 + shift_2] - plut[id001 + shift_2]);
                    yt1 = plut[id011 + shift_2] + r_d * (plut[id111 + shift_2] - plut[id011 + shift_2]);

                    zt0 = xt0 + g_d * (xt1 - xt0);
                    zt1 = yt0 + g_d * (yt1 - yt0);

                    float tmp_b = zt0 + b_d * (zt1 - zt0);

                    // 根据maps计算权重并应用到RGB值上
                    const float *pmap = map_channels[mi].ptr<float>();
                    pmap += cur_pos_map_offset;
                    float att_y0 = pmap[0] + (pmap[1] - pmap[0]) * alpha_j;
                    float att_y1 = pmap[map_channels[mi].cols] + (pmap[map_channels[mi].cols + 1] - pmap[map_channels[mi].cols]) * alpha_j;
                    float att = att_y0 + (att_y1 - att_y0) * alpha_i;

                    new_r += att * tmp_r;
                    new_g += att * tmp_g;
                    new_b += att * tmp_b;
                }

                // 将计算的新RGB值存储到输出图像的相应通道
                out_channels[0].at<float>(i, j) = new_r;
                out_channels[1].at<float>(i, j) = new_g;
                out_channels[2].at<float>(i, j) = new_b;
            }
        }

        // 合并三个颜色通道到最终的输出图像
        cv::Mat output_float;
        cv::merge(out_channels, output_float);
        if (accelerator) { // 使用小图做了加速
            cv::resize(output_float, output_float, cv::Size(src.cols, src.rows), 0, 0, cv::INTER_LINEAR);
            cv::add(image_hp, output_float, output_float);
        }

        // 将浮点型结果转换回原始类型
        output_float.convertTo(dst, src.type(), max_value);

        return true;
    }

    // 模板化的去噪函数
    template <typename T>
    cv::Mat inferDenoise_template(const cv::Mat &inp_rgb_, Ort::Session *model_ort_session, const Ort::MemoryInfo *cpu_arena_mem, const std::vector<const char *> &input_names, const std::size_t input_count,
                                  const std::vector<const char *> &output_names, const std::size_t output_count, int cv_depth, double max_value, const int tilesize = 256, const int tile_pad = 10) {
        // 控制参数
        cv::Mat out_rgb_ = inp_rgb_.clone(); // 初始化
        int scale = 1;                       // 不放大
        int extend_unit = 16;                // 保证推理图的宽高能被 16 整除
        int in_W = inp_rgb_.cols;            // 输入宽
        int in_H = inp_rgb_.rows;            // 输入高
        int tilesize_ = tilesize - tile_pad * 2;
        int inp_tile_pad_size = tilesize_ + tile_pad * 2;
        int out_tile_pad_size = inp_tile_pad_size * scale;

        // 根据数据类型创建对应的Mat
        int cv_type = cv_depth == CV_16U ? CV_16UC3 : CV_8UC3;
        std::shared_ptr<cv::Mat> in_tile_fixed_ptr = std::shared_ptr<cv::Mat>(new cv::Mat(inp_tile_pad_size, inp_tile_pad_size, cv_type, cv::Scalar(0, 0, 0)));
        std::shared_ptr<cv::Mat> hq_tile_fixed_ptr = std::shared_ptr<cv::Mat>(new cv::Mat(out_tile_pad_size, out_tile_pad_size, cv_type, cv::Scalar(0, 0, 0)));

        // 计算切片方式
        int tiles_x = std::ceil(float(in_W) / float(tilesize_));
        int tiles_y = std::ceil(float(in_H) / float(tilesize_));

        // 遍历所有切片
        for (int y = 0; y < tiles_y; ++y) {
            for (int x = 0; x < tiles_x; ++x) {
                // 计算切片坐标
                int tile_index = y * tiles_x + x + 1;
                int _serial_x_ = x * tilesize_;
                int _serial_y_ = y * tilesize_;

                int in_tile_x0 = _serial_x_;
                int in_tile_x1 = std::min(_serial_x_ + tilesize_, in_W);
                int in_tile_lw = in_tile_x1 - in_tile_x0;
                int in_tile_y0 = _serial_y_;
                int in_tile_y1 = std::min(_serial_y_ + tilesize_, in_H);
                int in_tile_lh = in_tile_y1 - in_tile_y0;

                int in_tile_pad_x0 = std::max(in_tile_x0 - tile_pad, 0);
                int in_tile_pad_x1 = std::min(in_tile_x1 + tile_pad, in_W);
                int in_tile_pad_lw = in_tile_pad_x1 - in_tile_pad_x0;
                int in_tile_pad_y0 = std::max(in_tile_y0 - tile_pad, 0);
                int in_tile_pad_y1 = std::min(in_tile_y1 + tile_pad, in_H);
                int in_tile_pad_lh = in_tile_pad_y1 - in_tile_pad_y0;

                // 获取切片空间
                int inp_tile_pad_width{inp_tile_pad_size}, inp_tile_pad_height{inp_tile_pad_size};
                int out_tile_pad_width{out_tile_pad_size}, out_tile_pad_height{out_tile_pad_size};
                std::shared_ptr<cv::Mat> in_tile_pad_mat_ptr{nullptr};
                std::shared_ptr<cv::Mat> hq_tile_pad_mat_ptr{nullptr};

                if (in_tile_pad_lw >= inp_tile_pad_size / 2 && in_tile_pad_lh >= inp_tile_pad_size / 2) {
                    in_tile_pad_mat_ptr = std::make_shared<cv::Mat>(*in_tile_fixed_ptr);
                    hq_tile_pad_mat_ptr = std::make_shared<cv::Mat>(*hq_tile_fixed_ptr);
                    (*in_tile_pad_mat_ptr).setTo(cv::Scalar(0, 0, 0));
                    (*hq_tile_pad_mat_ptr).setTo(cv::Scalar(0, 0, 0));
                } else {
                    inp_tile_pad_width = std::ceil(in_tile_pad_lw / float(extend_unit)) * extend_unit;
                    inp_tile_pad_height = std::ceil(in_tile_pad_lh / float(extend_unit)) * extend_unit;
                    out_tile_pad_width = inp_tile_pad_width * scale;
                    out_tile_pad_height = inp_tile_pad_height * scale;
                    in_tile_pad_mat_ptr = std::make_shared<cv::Mat>(inp_tile_pad_height, inp_tile_pad_width, cv_type, cv::Scalar(0, 0, 0));
                    hq_tile_pad_mat_ptr = std::make_shared<cv::Mat>(out_tile_pad_height, out_tile_pad_width, cv_type, cv::Scalar(0, 0, 0));
                }

                // 拷贝数据
                if ((in_tile_pad_x0 + inp_tile_pad_width) <= in_W && (in_tile_pad_y0 + inp_tile_pad_height) <= in_H) {
                    inp_rgb_(cv::Rect(in_tile_pad_x0, in_tile_pad_y0, inp_tile_pad_width, inp_tile_pad_height)).copyTo((*in_tile_pad_mat_ptr)(cv::Rect(0, 0, inp_tile_pad_width, inp_tile_pad_height)));
                } else {
                    inp_rgb_(cv::Rect(in_tile_pad_x0, in_tile_pad_y0, in_tile_pad_lw, in_tile_pad_lh)).copyTo((*in_tile_pad_mat_ptr)(cv::Rect(0, 0, in_tile_pad_lw, in_tile_pad_lh)));
                }

                // 准备推理输入数据
                std::vector<float> inp_tensor_values;
                cv::Mat in_tile_float;
                (*in_tile_pad_mat_ptr).convertTo(in_tile_float, CV_32FC3, 1.0 / max_value);
                if (!inpRGB2Tensor(in_tile_float, inp_tensor_values)) {
                    return out_rgb_;
                }

                std::vector<std::vector<std::int64_t>> inp_dims = {{1, 3, inp_tile_pad_height, inp_tile_pad_width}};
                auto inp_dim_data = inp_dims[0].data();
                auto inp_dim_size = inp_dims[0].size();
                auto inp_tensor_size = inp_tensor_values.size();
                std::vector<Ort::Value> inp_tensors;
                inp_tensors.emplace_back(Ort::Value::CreateTensor<float>(*cpu_arena_mem, inp_tensor_values.data(), inp_tensor_size, inp_dim_data, inp_dim_size));

                // 执行模型推理
                std::vector<int64_t> out_dim;
                auto out_tensors = model_ort_session->Run(Ort::RunOptions{nullptr}, input_names.data(), inp_tensors.data(), input_count, output_names.data(), output_count);
                if (out_tensors.empty()) {
                    return out_rgb_;
                }

                // 解析推理结果
                if (out_tensors[0].IsTensor()) {
                    auto out_info = out_tensors[0].GetTensorTypeAndShapeInfo();
                    for (auto i = 0; i < out_info.GetShape().size(); i++) {
                        out_dim.push_back(static_cast<int>(out_info.GetShape()[i]));
                    }
                }
                auto out_data = out_tensors[0].GetTensorMutableData<float>();
                auto out_c = out_dim[1];
                auto out_h = out_dim[2];
                auto out_w = out_dim[3];
                std::vector<cv::Mat> out_channels(out_c);
                for (int i = 0; i < out_c; ++i) {
                    out_channels[i] = cv::Mat(out_h, out_w, CV_32FC(1), (void *)(out_data));
                    out_data += out_h * out_w;
                }

                // 转换回原始类型
                cv::Mat temp_merged;
                cv::merge(out_channels, temp_merged);
                temp_merged.convertTo(*hq_tile_pad_mat_ptr, cv_depth, max_value);

                // 计算回写坐标
                int hq_tile_pad_x0 = (in_tile_x0 - in_tile_pad_x0) * scale;
                int hq_tile_pad_x1 = hq_tile_pad_x0 + in_tile_lw * scale;
                int hq_tile_pad_lw = hq_tile_pad_x1 - hq_tile_pad_x0;
                int hq_tile_pad_y0 = (in_tile_y0 - in_tile_pad_y0) * scale;
                int hq_tile_pad_y1 = hq_tile_pad_y0 + in_tile_lh * scale;
                int hq_tile_pad_lh = hq_tile_pad_y1 - hq_tile_pad_y0;

                int hq_tile_x0 = in_tile_x0 * scale;
                int hq_tile_x1 = in_tile_x1 * scale;
                int hq_tile_lw = hq_tile_x1 - hq_tile_x0;
                int hq_tile_y0 = in_tile_y0 * scale;
                int hq_tile_y1 = in_tile_y1 * scale;
                int hq_tile_lh = hq_tile_y1 - hq_tile_y0;

                // 将结果放回图像中
                cv::parallel_for_(cv::Range(0, hq_tile_lh), [&](const cv::Range &range) {
                    for (int y = range.start; y < range.end; ++y) {
                        (*hq_tile_pad_mat_ptr).row(hq_tile_pad_y0 + y).colRange(hq_tile_pad_x0, hq_tile_pad_x0 + hq_tile_pad_lw).copyTo(out_rgb_.row(hq_tile_y0 + y).colRange(hq_tile_x0, hq_tile_x0 + hq_tile_lw));
                    }
                });

                // 调试信息
                if (y % 10 == 0 && x % 10 == 0) {
                    cv::Mat tile_8bit;
                    char tile_filename[1024] = {'\0'};
                    if (cv_depth == CV_16U) {
                        (*in_tile_pad_mat_ptr).convertTo(tile_8bit, CV_8UC3, 1.0 / 257.0); // 255 * 257 = 65535
                    } else {
                        tile_8bit = *in_tile_pad_mat_ptr;
                    }
                    snprintf(tile_filename, sizeof(tile_filename), "DAE_%d_%d_inp.jpg", y, x);
                    std::uint8_t *in_tile_pixel = tile_8bit.data;
                    logger_->save_image(tile_filename, in_tile_pixel, tile_8bit.cols, tile_8bit.rows, 3);

                    if (cv_depth == CV_16U) {
                        (*hq_tile_pad_mat_ptr).convertTo(tile_8bit, CV_8UC3, 1.0 / 257.0); // 255 * 257 = 65535
                    } else {
                        tile_8bit = *hq_tile_pad_mat_ptr;
                    }
                    snprintf(tile_filename, sizeof(tile_filename), "DAE_%d_%d_out.jpg", y, x);
                    std::uint8_t *hq_tile_pixel = tile_8bit.data;
                    logger_->save_image(tile_filename, hq_tile_pixel, tile_8bit.cols, tile_8bit.rows, 3);
                }
            }
        }

        return out_rgb_;
    }

    // 定义随机裁剪缩放函数以保护结果数据
    cv::Mat randomCropAndScale(cv::Mat &image, double scaleMin = 0.99, double scaleMax = 1.01, int cropMin = 1, int cropMax = 5) {
        // 随机选择缩放因子
        double scale = scaleMin + static_cast<double>(rand()) / (static_cast<double>(RAND_MAX / (scaleMax - scaleMin)));

        // 随机选择裁剪像素
        int crop = cropMin + rand() % (cropMax - cropMin + 1);

        // 计算新的尺寸
        cv::Size newSize(static_cast<int>(image.cols * scale) - crop, static_cast<int>(image.rows * scale) - crop);

        // 防止尺寸为0
        if (newSize.width <= 0 || newSize.height <= 0) {
            return image; // 可以选择返回原图或抛出异常
        }

        // 缩放图像
        cv::Mat scaled;
        cv::resize(image, scaled, newSize);

        // 随机裁剪图像
        int x = rand() % (scaled.cols - newSize.width + 1);
        int y = rand() % (scaled.rows - newSize.height + 1);
        cv::Rect roi(x, y, newSize.width, newSize.height);

        // 返回裁剪后的图像
        return cv::Mat(scaled, roi);
    }

    // **************************************************************************************************************************
    // **************************************************************************************************************************
    // **************************************************************************************************************************

    // 结束后清理函数
    void clear() {
        // 重置初始化标志
        initialed_ = false;

        // 释放日志资源
        logger_ = nullptr;

        // 重置 ONNX 模型会话
        _rcl_1_rawlut_ort_session_.reset();
        _rc_d_ort_session_.reset();
        _rcl_2_stdlut_ort_session_.reset();

// release OpenGL
#ifdef USE_OPENGL
        general_computing_.clear();
#endif
    }

  private:
    // 初始化标志，默认为false
    bool initialed_{false};

    // 日志
    Logger *logger_{nullptr};

    // ONNX 环境配置
    const OrtConfig *ort_config_{nullptr};

    // 模型推理配置
    int num_cpu_threads_ = std::max(1u, std::thread::hardware_concurrency()), rawcvt_n_threads = -1; // 当前设备可用的最大线程数; 用于运算的线程数量，-1表示使用所有可用线程
    bool enable_raw_lut = true, usable_raw_lut = false;                                              // 布尔变量，表示是否启用调亮模型，并设置尚未被初始化
    bool enable_denoise = true, usable_denoise = false;                                              // 布尔变量，表示是否启用去噪模型，并设置尚未被初始化
    bool enable_std_lut = true, usable_std_lut = false;                                              // 布尔变量，表示是否启用调标准色模型，并设置尚未被初始化
    RAWCVTConfig::adjust_type rawcvt_adjust_type = RAWCVTConfig::NORMAL;                             // 使用枚举类型指定调色模式，默认为: NORMAL
    bool rawcvt_auto_level = false;                                                                  // TODO: 布尔变量，表示在后处理阶段是否进行自动色阶调整，默认不启用
    bool stdlut_adjust_custom = false;                                                               // 用来切换是否直接支持客户JPG调色的开关，当该值与run接口中的adjust_custom不一致时会重新初始化调色模型

    // 模型信息: _rcl_1_rawlut_, _rc_d_, _rcl_2_stdlut_, _rcl_2_stdylut_, _rcl_2_lutcls_
    std::unique_ptr<Ort::Session> _rcl_1_rawlut_ort_session_, _rc_d_ort_session_, _rcl_2_stdlut_ort_session_, _rcl_2_stylut_ort_session_, _rcl_2_lutcls_ort_session_;                       // 声明一个指向Ort::Session对象的唯一指针
    std::size_t _rcl_1_rawlut_inp_count_{0}, _rc_d_inp_count_{0}, _rcl_2_stdlut_inp_count_{0}, _rcl_2_stylut_inp_count_{0}, _rcl_2_lutcls_inp_count_{0};                                    // 输入计数器，初始化为0
    std::vector<std::int32_t> _rcl_1_rawlut_inp_chs_, _rc_d_inp_chs_, _rcl_2_stdlut_inp_chs_, _rcl_2_stylut_inp_chs_, _rcl_2_lutcls_inp_chs_;                                               // 输入通道数的向量
    std::vector<const char *> _rcl_1_rawlut_inp_names_, _rc_d_inp_names_, _rcl_2_stdlut_inp_names_, _rcl_2_stylut_inp_names_, _rcl_2_lutcls_inp_names_;                                     // 输入名称的向量
    std::vector<ONNXTensorElementDataType> _rcl_1_rawlut_inp_types_, _rc_d_inp_types_, _rcl_2_stdlut_inp_types_, _rcl_2_stylut_inp_types_, _rcl_2_lutcls_inp_types_;                        // 输入数据类型的向量
    std::vector<Ort::AllocatedStringPtr> _rcl_1_rawlut_inp_names_ptrs_, _rc_d_inp_names_ptrs_, _rcl_2_stdlut_inp_names_ptrs_, _rcl_2_stylut_inp_names_ptrs_, _rcl_2_lutcls_inp_names_ptrs_; // 指向输入名称的指针向量
    std::size_t _rcl_1_rawlut_out_count_{0}, _rc_d_out_count_{0}, _rcl_2_stdlut_out_count_{0}, _rcl_2_stylut_out_count_{0}, _rcl_2_lutcls_out_count_{0};                                    // 输出计数器，初始化为0
    std::vector<std::int32_t> _rcl_1_rawlut_out_chs_, _rc_d_out_chs_, _rcl_2_stdlut_out_chs_, _rcl_2_stylut_out_chs_, _rcl_2_lutcls_out_chs_;                                               // 输出通道数的向量
    std::vector<const char *> _rcl_1_rawlut_out_names_, _rc_d_out_names_, _rcl_2_stdlut_out_names_, _rcl_2_stylut_out_names_, _rcl_2_lutcls_out_names_;                                     // 输出名称的向量
    std::vector<ONNXTensorElementDataType> _rcl_1_rawlut_out_types_, _rc_d_out_types_, _rcl_2_stdlut_out_types_, _rcl_2_stylut_out_types_, _rcl_2_lutcls_out_types_;                        // 输出数据类型的向量
    std::vector<Ort::AllocatedStringPtr> _rcl_1_rawlut_out_names_ptrs_, _rc_d_out_names_ptrs_, _rcl_2_stdlut_out_names_ptrs_, _rcl_2_stylut_out_names_ptrs_, _rcl_2_lutcls_out_names_ptrs_; // 指向输出名称的指针向量

    // OpenGL 相关变量
    int shader_type_ = 1;
    int shaderProgram_ = -1;
    bool enable_opengl_{false};

// 添加GeneralComputing成员变量
#ifdef USE_OPENGL
    GeneralComputing general_computing_;
#endif
};

// RAWConversion构造函数
RAWConversion::RAWConversion() : inner_(new RAWConversion::RAWConversionInner()) {
    omp_set_schedule(omp_sched_static, 0); // 设置OpenMP的调度策略为静态
}

// 初始化函数，调用内部实现类的init函数
bool RAWConversion::init(const OrtConfig *ort_config, const std::string &key, const std::string &user_code, const std::string &prod_code, bool enable_opengl) { return inner_->init(ort_config, key, user_code, prod_code, enable_opengl); }

// 运行转换操作，调用内部实现类的run函数
bool RAWConversion::run(const RAWCVTConfig &config, const cv::Mat &src_image, cv::Mat &dst_image, std::uint32_t src_color_format, int &adjust_mode, bool adjust_custom) {
    return inner_->run(config, src_image, dst_image, src_color_format, adjust_mode, adjust_custom);
}

// 清理函数，调用内部实现类的clear函数
void RAWConversion::clear() { inner_->clear(); }

// RAWConversion析构函数，默认实现
RAWConversion::~RAWConversion() = default;

} // namespace pgrawcvt
