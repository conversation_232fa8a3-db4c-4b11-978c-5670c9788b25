//
//  PGGPUGeneralComputing
//
//  Created by <PERSON><PERSON>ing<PERSON><PERSON> on 2019/7/10.
//  Copyright © 2019 ZhangJingQi. All rights reserved.
//

#include <cmath>
#include <map>
#include <memory>
#include <vector>

#include "general_computing/general_computing/opengl/gles_device.hpp"

#include "general_computing.hpp"

#include "raw_conversion/raw_conversion/apply_ogl.hpp"

// add PGGC
namespace pgrawcvt {
using namespace pggc_scm;
class GeneralComputing::GeneralComputingInner {
  public:
    GeneralComputingInner() : device_(new gles_device()) {}
    ~GeneralComputingInner() {
        clear();
        device_.reset();
    }
    bool init() {
        device_->logger_ = logger_;
        if (!device_->init()) {
            return false;
        }

        initialed_ = true;
        return true;
    }

    void clear() {
        if (!initialed_) {
            return;
        }
        if (device_) {
            auto clear_result = device_->thread()->commit([&]() {
                for (size_t i = 0; i < 3; i++) {
                    release_propgram(shaderProgram_[i]);
                }
                return true;
            });

            if (!clear_result.get()) {
                logger_->log_info("[GC] clean gpu pipeline failed.");
                return;
            }
            device_->destroy();
        }

        initialed_ = false;
    }

    bool run(const void *psrc_data, void *pdst_data, int width, int height, int elem_type, const float *luts, std::vector<int64_t> &luts_shape, const float *maps, std::vector<int64_t> &maps_shape) {
        if (!initialed_) {
            return false;
        }

        auto run_result = device_->thread()->commit([&]() {
            logger_->latency_start();
            auto ret = triLinear_spatial_aware_opengl(shaderProgram_[elem_type], psrc_data, pdst_data, width, height, elem_type, luts, luts_shape, maps, maps_shape);

            logger_->latency_log("[GC] triLinear_spatial_aware_opengl");
            return ret;
        });

        auto result = run_result.get();
        return result;
    }

  public:
    std::shared_ptr<pgrawcvt::Logger> logger_ = std::make_shared<pgrawcvt::Logger>();

  private:
    bool initialed_{false};
    std::unique_ptr<gles_device> device_;
    int shaderProgram_[3] = {-1, -1, -1}; // u8 u16 f32 的shaderprogram
};

GeneralComputing::GeneralComputing() : inner_(new GeneralComputing::GeneralComputingInner()) {}

bool GeneralComputing::init() { return inner_->init(); }

bool GeneralComputing::run(const void *psrc_data, void *pdst_data, int width, int height, int elem_type, const float *luts, std::vector<int64_t> &luts_shape, const float *maps, std::vector<int64_t> &maps_shape) {
    return inner_->run(psrc_data, pdst_data, width, height, elem_type, luts, luts_shape, maps, maps_shape);
}

void GeneralComputing::set_log_callback(LogCallback log_func) { inner_->logger_->set_log_print_func(log_func); }

void GeneralComputing::clear() { inner_->clear(); }

GeneralComputing::~GeneralComputing() = default;

} // namespace pgrawcvt
