//
//  PGGeneralComputing
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/7/10.
//  Copyright © 2019 ZhangJingQi. All rights reserved.
//

#ifndef GENERAL_COMPUTING_GENERAL_COMPUTING_OPENGL_GLES_DEVICE_HPP_
#define GENERAL_COMPUTING_GENERAL_COMPUTING_OPENGL_GLES_DEVICE_HPP_

#include "general_computing/general_computing/opengl/gles_context.hpp"
#include "general_computing/general_computing/opengl/gles_thread_pool.hpp"
#include <cstring>
#include <memory>
#include <string>
#include <vector>

namespace pggc_scm {

class gles_device {
  public:
    std::vector<std::string> split_str(const std::string &s, char sep) {
        std::vector<std::string> output;
        std::string::size_type prev_pos = 0, pos = 0;
        while ((pos = s.find(sep, pos)) != std::string::npos) {
            std::string substring(s.substr(prev_pos, pos - prev_pos));
            output.push_back(substring);
            prev_pos = ++pos;
        }
        output.push_back(s.substr(prev_pos, pos - prev_pos));
        return output;
    }

    bool init() {
        gl_context_.logger_ = logger_;
        gl_thread_ = std::make_shared<thread_pool>(1);
        auto setup_result = gl_thread_->commit([&]() {
            if (!gl_context_.setup()) {
                logger_->log_info("[GC] gles_context setup failed.");
                return false;
            }
            if (gl_context_.activate()) {
                name_ = (const char *)glGetString(GL_RENDERER);
                logger_->log_info("[GC] GPU Device: %s", name_.c_str());
                CHECK_GL_ERROR();
                return true;
            }
            return false;
        });

        if (!setup_result.get()) {
            logger_->log_info("[GC] gles_context activate failed.");
            return false;
        }

        auto get_gpu_attr = gl_thread_->commit([&]() {
            const char *gl_ver = (const char *)glGetString(GL_VERSION);
            if (gl_ver != nullptr && strstr(gl_ver, "OpenGL ES")) {
                sscanf(gl_ver, "OpenGL ES %d.%d", &major_version_, &minor_version_);
                if (major_version_ >= 3) {
#if defined(GLES_OS_ANDROID)
                    gles30_support_ = load_gles30();
#else
                    gles30_support_ = true;
#endif
                }
            }
            CHECK_GL_ERROR();
            logger_->log_info("[GC] GL Version: %s", gl_ver);
            logger_->log_info("[GC] OpenGL ES 3.0 Support: %s", gles30_support_ ? "YES" : "NO");
            return true;
        });

        if (!get_gpu_attr.get()) {
            logger_->log_info("[GC] gles_context get_gpu_attr failed.");
            return false;
        }
        return true;
    }

    void destroy() {
        if (gl_thread_ != nullptr) {
            auto uninstall_op = gl_thread_->commit([&]() {
                gl_context_.deactivate();
                gl_context_.uninstall();
            });
            uninstall_op.get();
            gl_thread_ = nullptr;
        }
        gles30_support_ = false;
    }

    std::shared_ptr<thread_pool> thread() { return gl_thread_; }

    ~gles_device() { destroy(); }

  public:
    std::shared_ptr<pgrawcvt::Logger> logger_;

  private:
    bool gles30_support_{false};
    int major_version_{0};
    int minor_version_{0};
    std::string name_{"unknown"};
    gles_context gl_context_;
    std::shared_ptr<thread_pool> gl_thread_{nullptr};
};

} // namespace pggc_scm

#endif // GENERAL_COMPUTING_GENERAL_COMPUTING_OPENGL_GLES_DEVICE_HPP_
