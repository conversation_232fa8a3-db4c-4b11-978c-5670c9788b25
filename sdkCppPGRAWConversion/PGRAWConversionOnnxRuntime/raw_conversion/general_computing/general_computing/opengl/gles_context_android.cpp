//
//  PGGeneralComputing
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/7/10.
//  Copyright © 2019 ZhangJingQi. All rights reserved.
//

#include "general_computing/general_computing/opengl/gles_context.hpp"

namespace pggc_scm {

struct gles_context::context_manager {
    EGLConfig egl_config_{nullptr};
    EGLSurface egl_surface_{EGL_NO_SURFACE};
    EGLContext egl_context_{EGL_NO_CONTEXT};
    EGLDisplay egl_display_{EGL_NO_DISPLAY};
};

bool gles_context::setup() {
    if (ctx_manager_ != nullptr) {
        return false;
    }

    ctx_manager_ = std::make_shared<context_manager>();

    const EGLint config_attr[] = {EGL_RENDERABLE_TYPE, EGL_OPENGL_ES2_BIT, EGL_SURFACE_TYPE, EGL_PBUFFER_BIT, EGL_RED_SIZE, 8, EG<PERSON>_GREEN_SIZE, 8, EG<PERSON>_BLUE_SIZE, 8, EGL_ALPHA_SIZE, 8, EGL_DEPTH_SIZE, 16, EGL_NONE};

    EGLint num_configs;
    EGLint context_attr[] = {EGL_CONTEXT_CLIENT_VERSION, 3, EGL_NONE};
    EGLint egl_major_version, egl_minor_version;

    ctx_manager_->egl_display_ = eglGetDisplay(EGL_DEFAULT_DISPLAY);

    if (ctx_manager_->egl_display_ == EGL_NO_DISPLAY) {
        logger_->log_info("[GC] eglGetDisplay failed: %d", eglGetError());
        return false;
    }

    if (!eglInitialize(ctx_manager_->egl_display_, &egl_major_version, &egl_minor_version)) {
        logger_->log_info("[GC] eglInitialize failed: %d", eglGetError());
        return false;
    }

    logger_->log_info("[GC] EGL init with version %d.%d", egl_major_version, egl_minor_version);

    if (!eglChooseConfig(ctx_manager_->egl_display_, config_attr, &ctx_manager_->egl_config_, 1, &num_configs)) {
        logger_->log_info("[GC] eglChooseConfig failed: %d", eglGetError());
        return false;
    }

    ctx_manager_->egl_context_ = eglCreateContext(ctx_manager_->egl_display_, ctx_manager_->egl_config_, EGL_NO_CONTEXT, context_attr);
    if (ctx_manager_->egl_context_ == EGL_NO_CONTEXT) {
        context_attr[1] = 2;
        ctx_manager_->egl_context_ = eglCreateContext(ctx_manager_->egl_display_, ctx_manager_->egl_config_, EGL_NO_CONTEXT, context_attr);
        if (ctx_manager_->egl_context_ == EGL_NO_CONTEXT) {
            logger_->log_info("[GC] eglCreateContext failed: %d", eglGetError());
            return false;
        }
    }

    if (ctx_manager_->egl_surface_ != EGL_NO_SURFACE) {
        eglDestroySurface(ctx_manager_->egl_display_, ctx_manager_->egl_surface_);
        ctx_manager_->egl_surface_ = EGL_NO_SURFACE;
    }

    const EGLint surface_attr[] = {EGL_WIDTH, 4096, EGL_HEIGHT, 4096, EGL_NONE};

    ctx_manager_->egl_surface_ = eglCreatePbufferSurface(ctx_manager_->egl_display_, ctx_manager_->egl_config_, surface_attr);
    if (ctx_manager_->egl_surface_ == EGL_NO_SURFACE) {
        logger_->log_info("[GC] eglCreatePbufferSurface failed: %d", eglGetError());
        return false;
    }

    return true;
}

bool gles_context::uninstall() {
    if (ctx_manager_ == nullptr) {
        return false;
    }

    deactivate();
    if (ctx_manager_->egl_display_) {
        eglMakeCurrent(ctx_manager_->egl_display_, EGL_NO_SURFACE, EGL_NO_SURFACE, EGL_NO_CONTEXT);
    }

    if (ctx_manager_->egl_surface_ != EGL_NO_SURFACE) {
        eglDestroySurface(ctx_manager_->egl_display_, ctx_manager_->egl_surface_);
        ctx_manager_->egl_surface_ = EGL_NO_SURFACE;
    }

    if (ctx_manager_->egl_display_ != nullptr) {
        if (ctx_manager_->egl_context_ != nullptr) {
            eglDestroyContext(ctx_manager_->egl_display_, ctx_manager_->egl_context_);
        }
        eglTerminate(ctx_manager_->egl_display_);
    }

    ctx_manager_->egl_display_ = EGL_NO_DISPLAY;
    ctx_manager_->egl_context_ = EGL_NO_CONTEXT;
    ctx_manager_ = nullptr;
    return true;
}

bool gles_context::activate() {
    if (ctx_manager_ == nullptr) {
        return false;
    }

    assert(ctx_manager_->egl_display_ != EGL_NO_DISPLAY && ctx_manager_->egl_config_ != nullptr && ctx_manager_->egl_context_ != EGL_NO_CONTEXT && ctx_manager_->egl_surface_ != EGL_NO_SURFACE);

    if (!eglMakeCurrent(ctx_manager_->egl_display_, ctx_manager_->egl_surface_, ctx_manager_->egl_surface_, ctx_manager_->egl_context_)) {
        logger_->log_info("[GC] eglMakeCurrent failed: %d", eglGetError());
        return false;
    }

    return true;
}

bool gles_context::deactivate() {
    if (ctx_manager_ == nullptr) {
        return false;
    }

    if (ctx_manager_->egl_display_ != nullptr && ctx_manager_->egl_surface_ != nullptr) {
        if (!eglMakeCurrent(EGL_NO_DISPLAY, EGL_NO_SURFACE, EGL_NO_SURFACE, EGL_NO_CONTEXT)) {
            logger_->log_info("[GC] eglMakeCurrent failed: %d", eglGetError());
            return false;
        }
    }
    return true;
}

} // namespace pggc_scm
