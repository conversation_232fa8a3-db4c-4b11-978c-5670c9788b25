//
//  PGGeneralComputing
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/7/10.
//  Copyright © 2019 ZhangJingQ<PERSON>. All rights reserved.
//

#ifndef GENERAL_COMPUTING_GENERAL_COMPUTING_OPENGL_GLES_CONTEXT_HPP_
#define GENERAL_COMPUTING_GENERAL_COMPUTING_OPENGL_GLES_CONTEXT_HPP_

#include <memory>
// #include "general_computing/general_computing/utils/logger.hpp"
#include "raw_conversion/raw_conversion/logger.hpp"
// using pgrawcvt::Logger;

#ifdef __ANDROID__
#define GLES_OS_ANDROID 1
#elif __APPLE__
#include <TargetConditionals.h>
#if TARGET_OS_IPHONE || TARGET_IPHONE_SIMULATOR
#define GLES_OS_IOS 1
#else
#define GLES_OS_MACOS 1
#define OPENGL_STANDARD 1
#endif
#elif _WIN32
#define GLES_OS_WINDOWS 1
#define OPENGL_STANDARD 1
#elif __linux__
#define GLES_OS_LINUX 1
#define OPENGL_STANDARD 1
#endif // __ANDROID__

#if defined(GLES_OS_ANDROID)
#include "general_computing/general_computing/opengl/gles30_loader.h"
#include <EGL/egl.h>
#include <EGL/eglext.h>
#include <GLES2/gl2.h>
#include <GLES2/gl2ext.h>
#include <android/log.h>
#include <jni.h>
#elif defined(GLES_OS_IOS)
#include <OpenGLES/ES3/gl.h>
#include <OpenGLES/ES3/glext.h>
#elif defined(GLES_OS_MACOS)
#import <OpenGL/gl3.h>
#define GL_RGBA8_OES 0x8058
#elif defined(GLES_OS_WINDOWS)
#include <GL/glew.h>
#include <GLFW/glfw3.h>
#elif defined(GLES_OS_LINUX)
#include <GL/glew.h>
#include <GLFW/glfw3.h>
#endif // GLES_OS_MACOS

#define CHECK_GL_ERROR()                                                                                                                                                                                                                                 \
    {                                                                                                                                                                                                                                                    \
        GLenum err = glGetError();                                                                                                                                                                                                                       \
        if (err != GL_NO_ERROR) {                                                                                                                                                                                                                        \
            logger_->log_info("GLError: 0x%x, at %s, line %d", err, __FILE__, __LINE__ - 1);                                                                                                                                                             \
            assert(0);                                                                                                                                                                                                                                   \
        }                                                                                                                                                                                                                                                \
    }

// #define CHECK_GL_ERROR()

namespace pggc_scm {

class gles_context {
  protected:
    struct context_manager;

  public:
    bool setup();
    bool activate();
    bool deactivate();
    bool uninstall();

  private:
    std::shared_ptr<context_manager> ctx_manager_{nullptr};

  public:
    std::shared_ptr<pgrawcvt::Logger> logger_;
};

} // namespace pggc_scm

#endif // GENERAL_COMPUTING_GENERAL_COMPUTING_OPENGL_GLES_CONTEXT_HPP_
