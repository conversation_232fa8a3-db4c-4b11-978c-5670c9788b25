//
//  PGGeneralComputing
//
//  Created by Zhang<PERSON>ing<PERSON><PERSON> on 2019/7/10.
//  Copyright © 2019 ZhangJingQi. All rights reserved.
//

#include <cmath>

#include "general_computing/general_computing/opengl/gles_context.hpp"

#if defined(GLES_OS_MACOS)
#import <OpenGL/OpenGL.h>
#import <Cocoa/Cocoa.h>
#import <QuartzCore/CVDisplayLink.h>

#define kEAGLRenderingAPIOpenGLES2 2
#define kEAGLRenderingAPIOpenGLES3 3

@interface EAGLContext : NSObject {
    NSOpenGLContext *ctx;
    NSOpenGLPixelFormat *pf;
    CVDisplayLinkRef displayLink;
    NSPoint lastPoint;
}
- (void)clearDrawable;
@end

@implementation EAGLContext
- (id)initWithAPI:(int)apiVersion {
    self = [super init];
    if (self) {
        NSOpenGLPixelFormatAttribute attrs[] = {
            NSOpenGLPFADoubleBuffer,
            NSOpenGLPFADepthSize, 24,
            <PERSON><PERSON>penGLPFAStencilSize, 8,
            NSOpenGLPFAOpenGLProfile, NSOpenGLProfileVersion3_2Core,
            0
        };

        pf = [[NSOpenGLPixelFormat alloc] initWithAttributes:attrs];
        if (pf) {
            ctx = [[NSOpenGLContext alloc] initWithFormat:pf shareContext:nil];
            if (!ctx) {
                [pf release];
                pf = nil;
                [self release];
                return nil;
            }
        } else {
            [self release];
            return nil;
        }
    }
    return self;
}

- (void)clearDrawable {
    if (ctx) {
        NSOpenGLContext *currentContext = [NSOpenGLContext currentContext];
        [ctx makeCurrentContext];

        glFlush();
        glFinish();
        
        [ctx update];
        [ctx clearDrawable];

        if (currentContext && currentContext != ctx) {
            [currentContext makeCurrentContext];
        } else {
            [NSOpenGLContext clearCurrentContext];
        }
    }
}

- (void)dealloc {
    if (displayLink) {
        CVDisplayLinkStop(displayLink);
        CVDisplayLinkRelease(displayLink);
        displayLink = nil;
    }
    [self clearDrawable];

    if (ctx) {
        [ctx release];
        ctx = nil;
    }

    if (pf) {
        [pf release];
        pf = nil;
    }
    
    [super dealloc];
}

+ (EAGLContext *)currentContext {
    NSOpenGLContext *current = [NSOpenGLContext currentContext];
    if (current == nil) {
        return nil;
    }
    return nil;
}

+ (BOOL)setCurrentContext:(EAGLContext *)context {
    if (context == nil) {
        [NSOpenGLContext clearCurrentContext];
        return YES;
    }
    
    if (context->ctx != nil) {
        [context->ctx makeCurrentContext];
        return ([NSOpenGLContext currentContext] == context->ctx);
    }
    
    return NO;
}
@end

#elif defined(GLES_OS_IOS)
#import <OpenGLES/EAGL.h>
#endif

namespace pggc_scm {

struct gles_context::context_manager {
    EAGLContext *context_{nil};
    EAGLContext *backup_context_{nil};
};

bool gles_context::setup() {
    if (ctx_manager_ != nullptr) {
        return false;
    }

    ctx_manager_ = std::make_shared<context_manager>();

    @autoreleasepool {
        EAGLContext *context = [[EAGLContext alloc] initWithAPI:kEAGLRenderingAPIOpenGLES3];
        if (context == nil) {
            context = [[EAGLContext alloc] initWithAPI:kEAGLRenderingAPIOpenGLES2];
            if (context == nil) {
                ctx_manager_ = nullptr;
                return false;
            }
        }

        ctx_manager_->context_ = context;
    }

    return true;
}

bool gles_context::uninstall() {
    if (ctx_manager_ && ctx_manager_->context_ != nil) {
        @autoreleasepool {
            [ctx_manager_->context_ release];
        }
        ctx_manager_->context_ = nil;
    }
    
    ctx_manager_.reset();
    ctx_manager_ = nullptr;
    return true;
}

bool gles_context::activate() {
    if (ctx_manager_ == nullptr) {
        return false;
    }

    if (ctx_manager_->context_ == nil) {
        return false;
    }

    ctx_manager_->backup_context_ = nil;
    return [EAGLContext setCurrentContext:ctx_manager_->context_];
}

bool gles_context::deactivate() {
    if (ctx_manager_ == nullptr) {
        return false;
    }

    if (ctx_manager_->context_ == nil) {
        return false;
    }

    [EAGLContext setCurrentContext:nil];
    ctx_manager_->backup_context_ = nil;
    return true;
}

}  // namespace pggc_scm
