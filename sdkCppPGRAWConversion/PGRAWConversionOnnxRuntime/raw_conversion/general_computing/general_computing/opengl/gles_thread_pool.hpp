//
//  PGGeneralComputing
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/7/10.
//  Copyright © 2019 ZhangJingQi. All rights reserved.
//

#ifndef GENERAL_COMPUTING_GENERAL_COMPUTING_OPENGL_GLES_THREAD_POOL_HPP_
#define GENERAL_COMPUTING_GENERAL_COMPUTING_OPENGL_GLES_THREAD_POOL_HPP_

#include <cassert>
#include <functional>
#include <future> // NOLINT
#include <memory>
#include <mutex> // NOLINT
#include <queue>
#include <thread> // NOLINT
#include <utility>
#include <vector>

namespace pggc_scm {

class thread_pool {
  public:
    explicit thread_pool(std::uint32_t threads) {
        for (size_t i = 0; i < threads; ++i) {
            workers.emplace_back([this] {
                for (;;) {
                    std::function<void()> task;
                    {
                        std::unique_lock<std::mutex> lock(this->queue_mutex);
                        this->condition.wait(lock, [this] { return this->stop || !this->tasks.empty(); });
                        if (this->stop && this->tasks.empty()) {
                            return;
                        }
                        task = std::move(this->tasks.front());
                        this->tasks.pop();
                    }
                    task();
                }
            });
        }
    }

    template <class F, class... Args> auto commit(F &&f, Args &&...args) -> std::future<typename std::result_of<F(Args...)>::type> {
        using return_type = typename std::result_of<F(Args...)>::type;

        auto task = std::make_shared<std::packaged_task<return_type()>>(std::bind(std::forward<F>(f), std::forward<Args>(args)...));

        std::future<return_type> res = task->get_future();
        {
            std::unique_lock<std::mutex> lock(queue_mutex);
            if (stop) {
                throw std::runtime_error("commit on stopped pgdnn_threadpool");
                assert(0);
            }
            tasks.emplace([task]() { (*task)(); });
        }
        condition.notify_one();
        return res;
    }

    ~thread_pool() {
        {
            std::unique_lock<std::mutex> lock(queue_mutex);
            stop = true;
        }
        condition.notify_all();
        for (std::thread &worker : workers) {
            worker.join();
        }
    }

  private:
    bool stop{false};
    std::mutex queue_mutex;
    std::condition_variable condition;
    std::vector<std::thread> workers;
    std::queue<std::function<void()>> tasks;
};

} // namespace pggc_scm

#endif // GENERAL_COMPUTING_GENERAL_COMPUTING_OPENGL_GLES_THREAD_POOL_HPP_
