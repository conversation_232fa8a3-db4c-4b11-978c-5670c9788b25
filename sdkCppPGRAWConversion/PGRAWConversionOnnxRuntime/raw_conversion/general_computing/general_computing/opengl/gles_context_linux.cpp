//
//  PGGeneralComputing
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/7/10.
//  Copyright © 2019 ZhangJingQi. All rights reserved.
//

#include "general_computing/general_computing/opengl/gles_context.hpp"

#include <iostream>
namespace pggc_scm {

struct gles_context::context_manager {
    GLFWwindow *window{nullptr};
    GLFWwindow *backup_window{nullptr};
};

bool gles_context::setup() {
    if (ctx_manager_ != nullptr) {
        return false;
    }

    // 初始化GLFW
    if (!glfwInit()) {
        logger_->log_info("[GC] Failed to initialize GLFW");
        return false;
    }

    ctx_manager_ = std::make_shared<context_manager>();

    // 设置OpenGL版本和核心模式
    glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
    glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 3);
    glfwWindowHint(GLFW_OPENGL_PROFILE, GLFW_OPENGL_CORE_PROFILE);

    // 设置离线渲染相关的hint
    glfwWindowHint(GLFW_VISIBLE, GLFW_FALSE);      // 窗口不可见
    glfwWindowHint(GLFW_DECORATED, GLFW_FALSE);    // 无边框
    glfwWindowHint(GLFW_FOCUSED, GLFW_FALSE);      // 不获取焦点
    glfwWindowHint(GLFW_AUTO_ICONIFY, GLFW_FALSE); // 不自动最小化
    glfwWindowHint(GLFW_FLOATING, GLFW_TRUE);      // 总是置顶（避免被其他窗口影响）

    // 创建一个最小的隐藏窗口
    ctx_manager_->window = glfwCreateWindow(1, 1, "Offscreen Context", nullptr, nullptr);
    if (!ctx_manager_->window) {
        logger_->log_info("[GC] Failed to create GLFW window");
        uninstall();
        return false;
    }

    // 初次激活context
    if (!activate()) {
        uninstall();
        return false;
    }

    //   glewExperimental = GL_TRUE;
    if (glewInit() != GLEW_OK) {
        logger_->log_info("[GC] Failed to initialize GLEW");
        return false;
    }

    return true;
}

bool gles_context::uninstall() {
    if (ctx_manager_ != nullptr) {
        // 确保当前context已经取消激活
        deactivate();

        if (ctx_manager_->window) {
            glfwDestroyWindow(ctx_manager_->window);
            ctx_manager_->window = nullptr;
        }

        // 清理GLFW
        glfwTerminate();

        auto temp_ctx_manager = ctx_manager_;
        ctx_manager_ = nullptr;
    }
    return true;
}

bool gles_context::activate() {
    if (ctx_manager_ == nullptr || ctx_manager_->window == nullptr) {
        return false;
    }

    // 保存当前context
    ctx_manager_->backup_window = glfwGetCurrentContext();

    // 如果当前context不是我们的window
    if (ctx_manager_->backup_window != ctx_manager_->window) {
        glfwMakeContextCurrent(ctx_manager_->window);
        return true;
    }

    return true;
}

bool gles_context::deactivate() {
    if (ctx_manager_ == nullptr) {
        return false;
    }

    // 恢复之前的context
    glfwMakeContextCurrent(ctx_manager_->backup_window);
    ctx_manager_->backup_window = nullptr;

    return true;
}

} // namespace pggc_scm
