//
//  PGGeneralComputing
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/7/10.
//  Copyright © 2019 ZhangJingQi. All rights reserved.
//

#include "general_computing/general_computing/opengl/gles_context.hpp"

#include <iostream>
namespace pggc_scm {

struct gles_context::context_manager {
    GLFWwindow *window{nullptr};
    GLFWwindow *backup_window{nullptr};
};

bool gles_context::setup() {
    if (ctx_manager_ != nullptr) {
        return false;
    }

    // 设置 GLFW 错误回调，便于调试 Windows 相关问题
    glfwSetErrorCallback([](int error, const char *description) { std::cerr << "[GLFW Error] " << error << ": " << description << std::endl; });

    // 初始化GLFW
    if (!glfwInit()) {
        logger_->log_info("[GC] Failed to initialize GLFW");
        return false;
    }

    ctx_manager_ = std::make_shared<context_manager>();

    // Windows 特定的窗口 hints 设置
    // 这些设置有助于提高 Windows 平台的兼容性
    glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
    glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 3);
    glfwWindowHint(GLFW_OPENGL_PROFILE, GLFW_OPENGL_CORE_PROFILE);

    // Windows 特有的兼容性设置
    glfwWindowHint(GLFW_OPENGL_FORWARD_COMPAT, GLFW_TRUE);              // 前向兼容
    glfwWindowHint(GLFW_CONTEXT_NO_ERROR, GLFW_FALSE);                  // 启用错误检查
    glfwWindowHint(GLFW_DOUBLEBUFFER, GLFW_TRUE);                       // 双缓冲
    glfwWindowHint(GLFW_SAMPLES, 0);                                    // 禁用多重采样
    glfwWindowHint(GLFW_STEREO, GLFW_FALSE);                            // 禁用立体声
    glfwWindowHint(GLFW_SRGB_CAPABLE, GLFW_FALSE);                      // 禁用 sRGB
    glfwWindowHint(GLFW_REFRESH_RATE, GLFW_DONT_CARE);                  // 不关心刷新率
    glfwWindowHint(GLFW_CLIENT_API, GLFW_OPENGL_API);                   // 明确指定 OpenGL API
    glfwWindowHint(GLFW_CONTEXT_CREATION_API, GLFW_NATIVE_CONTEXT_API); // 使用原生上下文API

    // 设置离线渲染相关的hint
    glfwWindowHint(GLFW_VISIBLE, GLFW_FALSE);      // 窗口不可见
    glfwWindowHint(GLFW_DECORATED, GLFW_FALSE);    // 无边框
    glfwWindowHint(GLFW_FOCUSED, GLFW_FALSE);      // 不获取焦点
    glfwWindowHint(GLFW_AUTO_ICONIFY, GLFW_FALSE); // 不自动最小化
    glfwWindowHint(GLFW_FLOATING, GLFW_TRUE);      // 总是置顶（避免被其他窗口影响）

    // 创建一个最小的隐藏窗口
    ctx_manager_->window = glfwCreateWindow(1, 1, "Offscreen Context", nullptr, nullptr);
    if (!ctx_manager_->window) {
        logger_->log_info("[GC] Failed to create GLFW window");
        const char *error_desc;
        int error_code = glfwGetError(&error_desc);
        if (error_desc) {
            logger_->log_info("[GC] GLFW Error: %d - %s", error_code, error_desc);
        }
        uninstall();
        return false;
    }

    // 初次激活context
    if (!activate()) {
        logger_->log_info("[GC] Failed to activate OpenGL context");
        uninstall();
        return false;
    }

    // Windows 上需要设置 glewExperimental 来支持核心模式
    glewExperimental = GL_TRUE;
    GLenum glew_status = glewInit();
    if (glew_status != GLEW_OK) {
        logger_->log_info("[GC] Failed to initialize GLEW: %s", glewGetErrorString(glew_status));
        return false;
    }

    // 清除可能的 GLEW 初始化错误（这在 Windows 上很常见）
    while (glGetError() != GL_NO_ERROR) {
        // 清除所有累积的错误
    }

    // 验证 OpenGL 上下文是否正常工作
    const char *version = (const char *)glGetString(GL_VERSION);
    if (!version) {
        logger_->log_info("[GC] Failed to get OpenGL version");
        return false;
    }

    logger_->log_info("[GC] OpenGL Version: %s", version);
    logger_->log_info("[GC] OpenGL context setup successful on Windows");

    return true;
}

bool gles_context::uninstall() {
    if (ctx_manager_ != nullptr) {
        // 确保当前context已经取消激活
        deactivate();

        if (ctx_manager_->window) {
            glfwDestroyWindow(ctx_manager_->window);
            ctx_manager_->window = nullptr;
        }

        // 清理GLFW
        glfwTerminate();

        auto temp_ctx_manager = ctx_manager_;
        ctx_manager_ = nullptr;
    }
    return true;
}

bool gles_context::activate() {
    if (ctx_manager_ == nullptr || ctx_manager_->window == nullptr) {
        return false;
    }

    // 保存当前context
    ctx_manager_->backup_window = glfwGetCurrentContext();

    // 如果当前context不是我们的window
    if (ctx_manager_->backup_window != ctx_manager_->window) {
        glfwMakeContextCurrent(ctx_manager_->window);

        // 验证 context 切换是否成功
        if (glfwGetCurrentContext() != ctx_manager_->window) {
            logger_->log_info("[GC] Failed to make OpenGL context current");
            return false;
        }
        return true;
    }

    return true;
}

bool gles_context::deactivate() {
    if (ctx_manager_ == nullptr) {
        return false;
    }

    // 恢复之前的context
    glfwMakeContextCurrent(ctx_manager_->backup_window);
    ctx_manager_->backup_window = nullptr;

    return true;
}

} // namespace pggc_scm