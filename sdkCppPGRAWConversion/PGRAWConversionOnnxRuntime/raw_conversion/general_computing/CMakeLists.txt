# # Source Files
# file(GLO<PERSON> GC_SRC ${CMAKE_CURRENT_SOURCE_DIR}/general_computing/*.cpp)
# file(GLOB GC_HDR ${CMAKE_CURRENT_SOURCE_DIR}/general_computing/*.hpp)

list(APPEND GC_SRC ${CMAKE_CURRENT_SOURCE_DIR}/general_computing/opengl/gles_context.hpp)
list(APPEND GC_SRC ${CMAKE_CURRENT_SOURCE_DIR}/general_computing/opengl/gles_thread_pool.hpp)
list(APPEND GC_SRC ${CMAKE_CURRENT_SOURCE_DIR}/general_computing/opengl/gles_device.hpp)
# list(APPEND GC_SRC ${CMAKE_CURRENT_SOURCE_DIR}/general_computing/opengl/gles_mesh_info.hpp)
# list(APPEND GC_SRC ${CMAKE_CURRENT_SOURCE_DIR}/general_computing/opengl/gles_shader_info.hpp)
# list(APPEND GC_SRC ${CMAKE_CURRENT_SOURCE_DIR}/general_computing/opengl/gles_texture_info.hpp)
# list(APPEND GC_SRC ${CMAKE_CURRENT_SOURCE_DIR}/general_computing/opengl/gles_canvas_info.hpp)
# list(APPEND GC_SRC general_computing/pipelines/pipe_radial_blur.hpp)
if(APPLE) # Apple
    list(APPEND GC_SRC ${CMAKE_CURRENT_SOURCE_DIR}/general_computing/opengl/gles_context_apple.mm)
elseif(ANDROID) # Android
    list(APPEND GC_SRC ${CMAKE_CURRENT_SOURCE_DIR}/general_computing/opengl/gles30_loader.h)
    list(APPEND GC_SRC ${CMAKE_CURRENT_SOURCE_DIR}/general_computing/opengl/gles30_loader.c)
    list(APPEND GC_SRC ${CMAKE_CURRENT_SOURCE_DIR}/general_computing/opengl/gles_context_android.cpp)
elseif(WIN32) # Windows
    list(APPEND GC_SRC ${CMAKE_CURRENT_SOURCE_DIR}/general_computing/opengl/gles_context_windows.cpp)
else() # Linux
    list(APPEND GC_SRC ${CMAKE_CURRENT_SOURCE_DIR}/general_computing/opengl/gles_context_linux.cpp)
endif() # End of Apple

# # Utils Source Files
# file(GLOB UTILS_SRC ${CMAKE_CURRENT_SOURCE_DIR}/general_computing/utils/*.cpp)
# file(GLOB UTILS_HDR ${CMAKE_CURRENT_SOURCE_DIR}/general_computing/utils/*.hpp)

# # Pipelines Source Files
# file(GLOB PIPES_SRC ${CMAKE_CURRENT_SOURCE_DIR}/general_computing/pipelines/*.cpp)
# file(GLOB PIPES_HDR ${CMAKE_CURRENT_SOURCE_DIR}/general_computing/pipelines/*.hpp)

set(PGGC_SRC ${GC_SRC} ${GC_HDR} ${PIPES_SRC} ${PIPES_HDR} ${UTILS_SRC} ${UTILS_HDR} CACHE INTERNAL "PGGPU General Computing Source Files")
# add_library(PGGPUGeneralComputingStatic STATIC
#         ${GC_SRC} ${GC_HDR}
#         ${PIPES_SRC} ${PIPES_HDR}
#         ${UTILS_SRC} ${UTILS_HDR})
# set_property(TARGET PGGPUGeneralComputingStatic PROPERTY OUTPUT_NAME "PGGPUGeneralComputing")
# set_property(TARGET PGGPUGeneralComputingStatic PROPERTY CLEAN_DIRECT_OUTPUT 1)
# set_property(TARGET PGGPUGeneralComputingStatic PROPERTY C_STANDARD 99)
# set_property(TARGET PGGPUGeneralComputingStatic PROPERTY CXX_STANDARD 11)
# set_property(TARGET PGGPUGeneralComputingStatic PROPERTY COMPILE_FLAGS "${OPENMP_INCLUDE_FLAG} -Xpreprocessor -fopenmp -fPIC")
# set_property(TARGET PGGPUGeneralComputingStatic PROPERTY INCLUDE_DIRECTORIES ${CMAKE_SOURCE_DIR})
# set_property(TARGET PGGPUGeneralComputingStatic PROPERTY LINK_FLAGS "${PGGC_LINK_FLAGS}")
# target_link_libraries(PGGPUGeneralComputingStatic ${OPENMP_LINK_FLAG} ${PGGC_OS_LIBS})

# install(TARGETS PGGPUGeneralComputingStatic
#         ARCHIVE DESTINATION lib)

# install(FILES general_computing/general_computing.hpp DESTINATION include)

# # Build Applications
# add_subdirectory(app)
