{"cmake.debugConfig": {"args": ["--input", "../../../Data/Demo/10K+图片2.jpg", "--output", "../../../Data/Demo/10K+图片2.cpu.jpg"]}, "files.associations": {"concepts": "cpp", "ios": "cpp", "istream": "cpp", "locale": "cpp", "numeric": "cpp", "ostream": "cpp", "random": "cpp", "*.ipp": "cpp", "*.tcc": "cpp", "unordered_map": "cpp", "fstream": "cpp", "sstream": "cpp", "vector": "cpp", "cmath": "cpp", "__bit_reference": "cpp", "__bits": "cpp", "__config": "cpp", "__debug": "cpp", "__errc": "cpp", "__hash_table": "cpp", "__locale": "cpp", "__mutex_base": "cpp", "__node_handle": "cpp", "__split_buffer": "cpp", "__threading_support": "cpp", "__tree": "cpp", "__tuple": "cpp", "__verbose_abort": "cpp", "any": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "bitset": "cpp", "cctype": "cpp", "cfenv": "cpp", "cinttypes": "cpp", "clocale": "cpp", "codecvt": "cpp", "complex": "cpp", "condition_variable": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "exception": "cpp", "future": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "limits": "cpp", "list": "cpp", "map": "cpp", "memory": "cpp", "mutex": "cpp", "new": "cpp", "numbers": "cpp", "optional": "cpp", "queue": "cpp", "ratio": "cpp", "semaphore": "cpp", "set": "cpp", "shared_mutex": "cpp", "span": "cpp", "stack": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "string": "cpp", "string_view": "cpp", "strstream": "cpp", "system_error": "cpp", "thread": "cpp", "tuple": "cpp", "type_traits": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "unordered_set": "cpp", "valarray": "cpp", "variant": "cpp", "__nullptr": "cpp", "__string": "cpp", "chrono": "cpp", "compare": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory_resource": "cpp", "utility": "cpp", "charconv": "cpp", "execution": "cpp"}}